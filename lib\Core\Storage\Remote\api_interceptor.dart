import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../Config/Routes/route_name.dart';
import '../../../main.dart';
import '../Local/local_storage_service.dart';
import '../Local/local_storage_keys.dart';

class ApiInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String? token = LocalStorageService.getValue(LocalStorageKeys.token);
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'bearer $token';
    }
    debugPrint('🚀 Request: ${options.method} ${options.uri}');
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint('✅ Response: ${response.statusCode} ${response.requestOptions.uri}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    debugPrint('❌ API Error: ${err.response?.statusCode} - ${err.message}');

    if (err.response?.statusCode == 401) {
      _handleUnauthorized();
    }

    handler.next(err);
  }

  void _handleUnauthorized() {
    debugPrint('🔴 Unauthorized! Logging out...');
    LocalStorageService.removeValue(LocalStorageKeys.token);
     kNavigationService.clearAndNavigateTo(AppRoutes.authentication);
  }
}
