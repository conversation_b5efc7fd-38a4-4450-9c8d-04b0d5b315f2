// import 'package:flutter/material.dart';
//
// class StabilityIndicator extends StatelessWidget {
//   final bool isStable;
//
//   const StabilityIndicator({
//     super.key,
//     required this.isStable,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(8),
//       decoration: BoxDecoration(
//         color: Colors.black.withOpacity(0.7),
//         borderRadius: BorderRadius.circular(10),
//       ),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(
//             Icons.phone_android,
//             color: isStable ? Colors.green : Colors.yellow,
//           ),
//           const SizedBox(width: 8),
//           Text(
//             isStable ? "الهاتف مستقر" : "حافظ على ثبات الهاتف",
//             style: TextStyle(
//               color: isStable ? Colors.green : Colors.yellow,
//               fontSize: 14,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }