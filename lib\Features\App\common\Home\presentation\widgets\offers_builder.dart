import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../Config/Assets/image_png.dart';
import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';

class CustomOffersWidget extends StatelessWidget {
  const CustomOffersWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isRtl = context.isRtl;
    final local = context.local;
    return SliverToBoxAdapter(
      child: Padding(
        padding:AppConfig.customPadding,
        child: Stack(
          clipBehavior: Clip.hardEdge,
          fit: StackFit.passthrough,
          children: [
            Container(
              width: 327.w,
              height: 172.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                image: DecorationImage(
                  image: AssetImage(
                    AppImagesPng.offer,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              top: 19.h,
              right: isRtl ? 12.w : null,
              left: isRtl ? null : 12.w,
              child: Container(
                height: 22.h,
                width: 62.w,
                clipBehavior: Clip.antiAliasWithSaveLayer,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadiusDirectional.circular(50.r),
                ),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(5).r,
                    child: Text(
                      local.offerPrice,
                      style: AppTextStyles.bodyXtraSmallSemiBold
                          .copyWith(color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: 53.h,
              right: isRtl ? 12.w : null,
              left: isRtl ? null : 12.w,
              width: 160.w,
              child: Text(
                local.offerTittle,
                maxLines: 2,
                style: AppTextStyles.h6Bold.copyWith(color: Colors.white),
              ),
            ),
            Positioned(
              bottom: 19.h,
              right: isRtl ? 12.w : null,
              left: isRtl ? null : 12.w,
              child: TextButton(
                onPressed: () {},
                child: Text(
                  local.checkout,
                  style: AppTextStyles.bodySmallSemiBold
                      .copyWith(color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
