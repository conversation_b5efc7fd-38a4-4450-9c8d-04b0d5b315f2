import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../Cubit/user_cubit.dart';
import '../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_constants.dart';
import '../../../../../Core/Utils/Extensions/widget_extension.dart';
import '../../../../../Core/Utils/Widget/TextField/password_text__form_field.dart';
import '../../../../../Core/Utils/Widget/Massages/custom_scaffold_messenger.dart';
import '../../../../Global/Authentication/presentation/components/build_social_login_button.dart';
import '../../data/models/update_password.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

final formKey = GlobalKey<FormState>();
final TextEditingController currentController = TextEditingController();
final TextEditingController newController = TextEditingController();
final TextEditingController verifyController = TextEditingController();

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  @override
  void initState() {
    currentController.text = "";
    newController.text = "";
    verifyController.text = "";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final local = context.local;
    return [
      SliverList.list(children: [
        Form(
          key: formKey,
          child: Column(
            children: [
              50.verticalSpace,
              CustomPasswordTextFromField(
                controller: currentController,
                fieldId: AppConstants.passwordUser1,
                showForgetMessage: false,
                hintText: local.CurrentPassword,
                isLogin: false,
              ),
              CustomPasswordTextFromField(
                controller: newController,
                fieldId: AppConstants.passwordUser2,
                showForgetMessage: false,
                isLogin: false,
                hintText: local.NewPassword,
              ),
              CustomPasswordTextFromField(
                controller: verifyController,
                fieldId: AppConstants.passwordUser2,
                showForgetMessage: false,
                hintText: local.ConfirmationPassword,
                isLogin: false,
              ),
              30.verticalSpace,
              CustomBuildButtonApp(
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    if (newController.text != verifyController.text) {
                      showCustomSnackBar(
                          context, "Password not match", SnackBarType.error);
                      return;
                    }
                    final UpdatePassword updatePasswordUser = UpdatePassword(
                      oldPassword: currentController.text,
                      newPassword: newController.text,
                      passwordConfirmation: verifyController.text,
                    );
                    context
                        .read<UserCubit>()
                        .updatePassword(context, updatePasswordUser);
                  }
                },
                isSpace: false,
                text: local.Save,
                backgroundColor: AppColors.primaryColor,
              )
            ],
          ),
        )
      ])
    ].styledAppPages(
      withAll: true,
      appbar: true,
      widgetAppbar: AppBar(
        title: Text(local.ChangePassword),
        centerTitle: true,
      ),
    );
  }
}
