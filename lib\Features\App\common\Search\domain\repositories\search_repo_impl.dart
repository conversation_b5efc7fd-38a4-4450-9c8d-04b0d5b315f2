import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Source/search_sources.dart';

import '../../data/repositories/search_repo.dart';


class SearchRepoImpl implements SearchRepo {

  @override
  Future<Either<Failure, List<ReviewProductsModel>>> searchProducts() async{
    try {
      final response = await SearchSources.searchProducts();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, List<ReviewProductsModel>>> searchProductsByFilters({required String category}) {
    throw UnimplementedError();
  }
}
