import 'package:dartz/dartz.dart';
import '../Models/create_orders.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';


import '../Models/carpenters_model.dart';
import '../Models/get_all_services_model.dart';
import '../Models/get_carpenter_available_model.dart';

abstract class CarpenterServicesRepo {
  Future<Either<Failure,List<GetAllServicesModel>>> getAllServices() ;
  Future<Either<Failure,List<CarpentersModel>>> getCarpenters(PostCarpenterAvailableModel post) ;
  Future<Either<Failure,bool>> createOrders(CreateOrdersModel post) ;
}
