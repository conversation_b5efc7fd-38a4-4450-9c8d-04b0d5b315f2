import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../Config/Assets/image_png.dart';
import '../../Enums/numbers_of_cross_axis_count.dart';
import 'dart:io';

const Widget _buildIcon = Center(child: Icon(Icons.error));
const Center _loading = Center(child: CircularProgressIndicator());

class BuildImageAssets extends StatelessWidget {
  final String? url;
  final String? file;
  final String? svg;
  final String? svgUrl;
  final String? png;
  final BoxFit? fit;
  final double? height;
  final double? width;
  final Widget? widgetNull;
  final bool? setDefaultImage;
  final Color? color;
  const BuildImageAssets({
    super.key,
    this.url,
    this.height,
    this.width,
    this.svg,
    this.png,
    this.svgUrl,
    this.file,
    this.fit,
    this.widgetNull,
    this.setDefaultImage = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final image = {
      ImageType.svgAsset: svg,
      ImageType.svgNetwork: svgUrl,
      ImageType.asset: png,
      ImageType.network: url,
      ImageType.file: file,
    }.entries.firstWhere(
      (entry) =>
          entry.value != null &&
          entry.value!.isNotEmpty &&
          !entry.value!.contains("default.jpg"),
      orElse: () {
        if (setDefaultImage == true) {
          return MapEntry(ImageType.asset, AppImagesPng.icon);
        }
        return MapEntry(ImageType.none, null);
      },
    );

    if (image.value == null) {
      return SizedBox(
          height: height, width: width, child: widgetNull ?? _buildIcon);
    }

    switch (image.key) {
      case ImageType.svgAsset:
        return SvgPicture.asset(
          image.value!,
          theme: SvgTheme(currentColor: color ?? Colors.transparent),
          height: height,
          width: width,
          fit: fit ?? BoxFit.contain,
          placeholderBuilder: (_) => _loading,
          errorBuilder: (_, error, stackTrace) => _buildIcon,
        );
      case ImageType.svgNetwork:
        return SvgPicture.network(
          image.value!,
          height: height,
          width: width,
          fit: fit ?? BoxFit.contain,
          placeholderBuilder: (_) => _loading,
          errorBuilder: (_, error, stackTrace) => _buildIcon,
        );
      case ImageType.asset:
        return Image.asset(
          image.value!,
          height: height,
          width: width,
          fit: fit ?? BoxFit.contain,
          errorBuilder: (_, __, ___) => _buildIcon,
        );
      case ImageType.network:
        return CachedNetworkImage(
          imageUrl: image.value!,
          height: height,
          width: width,
          fit: fit ?? BoxFit.contain,
          filterQuality: FilterQuality.high,
          placeholder: (_, url) => _loading,
          errorWidget: (_, url, error) => _buildIcon,
        );
      case ImageType.file:
        return Image.file(
          File(image.value!),
          height: height,
          width: width,
          fit: fit ?? BoxFit.contain,
          errorBuilder: (_, __, ___) => _buildIcon,
        );
      default:
        return SizedBox(
            height: height, width: width, child: widgetNull ?? _buildIcon);
    }
  }
}
