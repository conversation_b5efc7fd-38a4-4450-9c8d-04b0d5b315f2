import 'package:dartz/dartz.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Models/get_all_services_model.dart';
import '../repository/carpenter_services_repo_impl.dart';

class CarpenterServicesUseCase {
  final CarpenterServicesRepoImpl repoImpl ;

  CarpenterServicesUseCase(this.repoImpl);

    Future<Either<Failure, List<GetAllServicesModel>>> call()async => await repoImpl.getAllServices();
}
