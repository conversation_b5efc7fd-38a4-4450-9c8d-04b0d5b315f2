import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/entities/product_details_model.dart';
import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Utils/Extensions/rating_summary.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../generated/l10n.dart';
import '../Manager/product_details_cubit.dart';

class ProductInformation extends StatelessWidget {
  const ProductInformation({super.key});

  @override
  Widget build(BuildContext context) {
    final details = context.read<ProductDetailsCubit>().state.product;
    if (details == null) return SizedBox.shrink();
    final S local = context.local;

    final statusProducts = details.status == 1;

    return ListView(
      shrinkWrap: true,
      physics: AppConfig.physicsCustomScrollView,
      children: [
        _buildTitleAndStock(
            details.name, details.quantity.toString(), statusProducts, local),
        if (details.dimensions.isNotEmpty) ...[
          8.verticalSpace,
          _buildText("${details.dimensions} ${local.CM}"),
        ],
        8.verticalSpace,
        _buildPrice(details),
        12.verticalSpace,
        _buildText("${details.description}\n---\n${details.additionalInfo}"),
        16.verticalSpace,
        if (details.colors.isNotEmpty) ...[
          _buildSectionTitle(local.Color),
          8.verticalSpace,
          _buildColorOptions(details.colors, context),
          16.verticalSpace,
        ],
        if (details.sizes.isNotEmpty) ...[
          _buildSectionTitle(local.Size),
          8.verticalSpace,
          _buildSizeOptions(details.sizes, context),
        ],
        8.verticalSpace,
        details.getRatingSummary()
      ],
    );
  }

  Widget _buildTitleAndStock(
      String name, String quantity, bool inStock, S local) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            name,
            maxLines: name.length,
            style:
                AppTextStyles.h3Bold.copyWith(color: AppColors.secondaryColor),
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: inStock
                ? AppColors.success.withAlpha(100)
                : AppColors.error.withAlpha(100),
            borderRadius: BorderRadius.circular(12).r,
          ),
          child: Row(
            children: [
              Text(
                inStock ? local.InStock : local.OutStock,
                style: AppTextStyles.bodySmallSemiBold
                    .copyWith(color: AppColors.secondaryColor),
              ),
              5.horizontalSpace,
              Text(
                "($quantity)",
                style: AppTextStyles.bodyXtraSmallBold
                    .copyWith(color: AppColors.secondaryColor),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPrice(ViewProductDetails product) {
    if (product.discountPrice.isNotEmpty) {
      return Row(
        children: [
          Text(
            "\$ ${product.discountPrice}",
            style: AppTextStyles.bodyMediumBold
                .copyWith(color: AppColors.textColorBlack),
          ),
          8.horizontalSpace,
          Text(
            "\$ ${product.price}",
            style: AppTextStyles.bodyMediumBold.copyWith(
              decoration: TextDecoration.lineThrough,
              color: AppColors.grayscale70,
            ),
          ),
        ],
      );
    }
    return Text(
      "\$ ${product.price}",
      style: AppTextStyles.bodyMediumBold
          .copyWith(color: AppColors.textColorBlack),
    );
  }

  Widget _buildColorOptions(List<ColorModel> colors, BuildContext context) {
    final selectedColors =
        context.read<ProductDetailsCubit>().state.colors ?? [];

    return Row(
      children: colors.map((color) {
        final isSelected = selectedColors == color;

        return GestureDetector(
          onTap: () {
            context.read<ProductDetailsCubit>().choseColor(color);
          },
          child: _convertHexToColor(
            color.code.toString(),
            selected: isSelected,
          ),
        );
      }).toList(),
    );
  }

  Widget _convertHexToColor(String colorHex, {required bool selected}) {
    final color = int.parse(colorHex.replaceFirst("#", "0xff"));
    return Container(
      margin: EdgeInsets.only(right: 8).w,
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: Color(color),
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primaryColor,
          width: selected ? 3 : 0,
          strokeAlign: .5,
        ),
      ),
    );
  }

  Widget _buildSizeOptions(List<SizeModel> sizes, BuildContext context) {
    final selectedSize = context.watch<ProductDetailsCubit>().state.size ?? [];

    return Row(
      children: sizes.map((size) {
        final isSelected = selectedSize == size;

        return GestureDetector(
          onTap: () {
            context.read<ProductDetailsCubit>().choseSize(size);
          },
          child: _buildSizeOption(
            size.name,
            selected: isSelected,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSizeOption(String size, {required bool selected}) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: selected ? Colors.black : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.black),
      ),
      child: Text(
        size,
        style: TextStyle(
            color: selected ? Colors.white : Colors.black, fontSize: 14.sp),
      ),
    );
  }

  Widget _buildText(String text) {
    return Text(
      text,
      maxLines: text.length,
      style: AppTextStyles.bodySmallSemiBold
          .copyWith(color: AppColors.textColorBlack),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      maxLines: title.length,
      style: AppTextStyles.h4Bold.copyWith(color: AppColors.textColorBlack),
    );
  }
}
