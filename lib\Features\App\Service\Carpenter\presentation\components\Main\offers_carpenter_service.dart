import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../../../Core/Utils/Widget/Animations/build_animatedview_list_box.dart';
import '../../Cubit/CarpenterService/carpenter_service_cubit.dart';
import '../../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../data/Models/offers_services_carpenter.dart';
import '../../../../../../../Config/Assets/image_png.dart';
import '../../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../../Core/Utils/Widget/Images/build_image.dart';
import '../../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../../generated/l10n.dart';

class OffersCarpenterService extends StatelessWidget {
  const OffersCarpenterService({super.key});

  @override
  Widget build(BuildContext context) {
     OffersModel offer =  OffersModel(
         name: 'Name Services',
         discount: '00',
         description: 'Descriptions Services',
         id: 0) ;
     List<OffersModel> offers =
        context.read<CarpenterServiceCubit>().state.bestOffers??[] ;
    final local = context.local;
    bool isLoading =
        context.watch<CarpenterServiceCubit>().state.loading ?? true;

    return SizedBox(
      height: 180.h,
      child: isLoading ? _buildLoadingDataStack(isLoading, offer, local) :ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: offers.length,
        separatorBuilder: (context, index) => SizedBox(width: 10.w),
        itemBuilder: (context, index) {
          return BuildAnimatedviewListBox(index: index, child: _buildData(index, isLoading, offers, local));
        },
      ),
    );
  }

  Stack _buildData(int index, bool isLoading, List<OffersModel> offers, S local) {
    return Stack(
          children: [
            OffersModel.image(index),
            Positioned(
              right: 20.w,
              height: 180.h,
              child: BuildImageAssets(
                png: AppImagesPng.carpenter,
              ),
            ),
            Positioned(
              left: 35.w,
              top: 65.h,
              child: Skeletonizer(
                enabled: isLoading,
                child: Text(
                  offers[index].name,
                  style: AppTextStyles.bodyLargeBold.copyWith(
                    color: AppColors.textColorWhite,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 35.w,
              bottom: 50.h,
              child: Skeletonizer(
                enabled: isLoading,
                child: Text(
                  "${offers[index].discount.split(".00").first} % ${local.offerPrice}",
                  style: AppTextStyles.h3Bold.copyWith(
                    color: AppColors.textColorWhite,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 10.w,
              bottom: 18.h,
              child: Skeletonizer(
                enabled: isLoading,
                child: SizedBox(
                  width: 150.w,
                  child: Text(
                    offers[index].description,
                    maxLines: 2,
                    style: AppTextStyles.bodyMediumBold.copyWith(
                      color: AppColors.textColorWhite,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
  }

  Stack _buildLoadingDataStack(bool isLoading, OffersModel offer, S local) => _buildData(0, isLoading, [offer], local);
}
