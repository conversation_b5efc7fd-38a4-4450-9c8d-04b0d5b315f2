// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import '../manager/banner/banner_cubit.dart';
//
// import '../../../../Core/Resources/list.dart';
//
// import 'package:flutter/material.dart';
//
// class BannerCarouselWidget extends StatelessWidget {
//   const BannerCarouselWidget({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => BannerCubit(),
//       child: BlocBuilder<BannerCubit, int>(
//         builder: (context, state) {
//           return Column(
//             children: [
//               Align(
//                 alignment: Alignment.topCenter,
//                 child: CarouselSlider(
//                   options: CarouselOptions(
//                     viewportFraction: 1,
//                     scrollDirection: Axis.horizontal,
//                     height: 0.4.sh,
//                     initialPage: AppList.itemBanners.length,
//                     onPageChanged: (index, reason) {
//                       context.read<BannerCubit>().changeIndexPageBanners(index);
//                     },
//                   ),
//                   items: AppList.itemBanners,
//                 ),
//               ),
//               10.verticalSpace,
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: AppList.itemBanners.asMap().entries.map((entry) {
//                   return Container(
//                     width: state ==
//                             entry.key
//                         ? 20.0.w
//                         : 8.0.w,
//                     height: state ==
//                             entry.key
//                         ? 10.0.h
//                         : 8.0.h,
//                     margin: const EdgeInsets.symmetric(
//                         vertical: 8.0, horizontal: 4.0).w,
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadiusDirectional.circular(15.r),
//                       shape: BoxShape.rectangle,
//                       color: state ==
//                               entry.key
//                           ? Colors.blueAccent
//                           : Colors.grey,
//                     ),
//                   );
//                 }).toList(),
//               ),
//             ],
//           );
//         },
//       ),
//     );
//   }
// }
