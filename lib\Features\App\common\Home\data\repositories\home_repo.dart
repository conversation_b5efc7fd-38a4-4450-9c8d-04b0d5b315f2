import 'package:dartz/dartz.dart';


import '../../../ProductsDetails/data/entities/product_details_model.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

abstract class HomeRepo {

    Future<Either<Failure, List<ReviewProductsModel>>> getProductIsFeatured();
    Future<Either<Failure, List<ReviewProductsModel>>> getProductIsPopular();
    Future<Either<Failure, ViewProductDetails>> getProductDetails(int id);
}