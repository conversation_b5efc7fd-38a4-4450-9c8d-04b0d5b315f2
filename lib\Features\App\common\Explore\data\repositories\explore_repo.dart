import 'package:dartz/dartz.dart';
import '../Model/all_categories.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';



abstract class ExploreRepo {
  Future<Either<Failure, List<AllCategories>>> getAllCategories();

  Future<Either<Failure, List<ReviewProductsModel>>>
      getCategoriesDetails({required int id});
}
