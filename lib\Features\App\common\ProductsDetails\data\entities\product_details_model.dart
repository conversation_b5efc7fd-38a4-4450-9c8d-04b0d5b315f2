class ViewProductDetails {
  ViewProductDetails({
    required this.id,
    required this.name,
    // required this.slug,
    // required this.categoryId,
    required this.description,
    required this.additionalInfo,
    required this.price,
    required this.discountPrice,
    required this.dimensions,
    // required this.featured,
    // required this.popular,
    required this.status,
    required this.quantity,
    required this.model3D,
    // required this.createdAt,
    // required this.updatedAt,
    required this.images,
    required this.colors,
    required this.sizes,
    required this.materials,
    required this.reviews,
    required this.primaryImage,
  });

  final int id;
  final String name;

  // final String slug;
  // final int categoryId;
  final String description;
  final String additionalInfo;
  final String price;
  final String discountPrice;
  final String dimensions;

  // final int featured;
  // final int popular;
  final int status;
  final int quantity;
  final dynamic model3D;

  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  final List<ImageModel> images;
  final List<ColorModel> colors;
  final List<SizeModel> sizes;
  final List<Material> materials;
  final List<Review>? reviews;
  final ImageModel? primaryImage;

  factory ViewProductDetails.fromJson(Map<String, dynamic> json) {
    return ViewProductDetails(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      // slug: json["slug"] ?? "",
      // categoryId: json["category_id"] ?? 0,
      description: json["description"] ?? "",
      additionalInfo: json["additional_info"] ?? "",
      price: json["price"] ?? "",
      discountPrice: json["discount_price"] ?? "",
      dimensions: json["dimensions"] ?? "",
      // featured: json["featured"] ?? 0,
      // popular: json["popular"] ?? 0,
      status: json["status"] ?? 0,
      quantity: json["quantity"] ?? 0,
      model3D: json["model_3d"],
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      images: json["images"] == null
          ? []
          : List<ImageModel>.from(
              json["images"]!.map((x) => ImageModel.fromJson(x))),
      colors: json["colors"] == null
          ? []
          : List<ColorModel>.from(
              json["colors"]!.map((x) => ColorModel.fromJson(x))),
      sizes: json["sizes"] == null
          ? []
          : List<SizeModel>.from(
              json["sizes"]!.map((x) => SizeModel.fromJson(x))),
      materials: json["materials"] == null
          ? []
          : List<Material>.from(
              json["materials"]!.map((x) => Material.fromJson(x))),
      reviews: json["reviews"] == null
          ? []
          : List<Review>.from(json["reviews"]!.map((x) => Review.fromJson(x))),
      primaryImage: json["primary_image"] == null
          ? null
          : ImageModel.fromJson(json["primary_image"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        // "slug": slug,
        // "category_id": categoryId,
        "description": description,
        "additional_info": additionalInfo,
        "price": price,
        "discount_price": discountPrice,
        "dimensions": dimensions,
        // "featured": featured,
        // "popular": popular,
        "status": status,
        "quantity": quantity,
        "model_3d": model3D,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        "images": images.map((x) => x.toJson()).toList(),
        "colors": colors.map((x) => x.toJson()).toList(),
        "sizes": sizes.map((x) => x.toJson()).toList(),
        "materials": materials.map((x) => x.toJson()).toList(),
        "reviews": reviews?.map((x) => x.toJson()).toList(),
        "primary_image": primaryImage?.toJson(),
      };

  ViewProductDetails copyWith({
    List<Review>? reviews,
  }) {
    return ViewProductDetails(
      id: id,
      name: name,
      description: description,
      additionalInfo: additionalInfo,
      price: price,
      discountPrice: discountPrice,
      dimensions: dimensions,
      status: status,
      quantity: quantity,
      model3D: model3D ?? model3D,
      images: images,
      colors: colors,
      sizes: sizes,
      materials: materials,
      reviews: reviews ?? this.reviews,
      primaryImage: primaryImage,
    );
  }
}

class ColorModel {
  ColorModel({
    this.id,
    this.name,
    this.code,
    // required this.createdAt,
    // required this.updatedAt,
    // required this.pivot,
  });

  final int? id;
  final String? name;
  final String? code;

  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  // final ColorPivot? pivot;

  factory ColorModel.fromJson(Map<String, dynamic> json) {
    return ColorModel(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      code: json["code"] ?? "",
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      // pivot: json["pivot"] == null ? null : ColorPivot.fromJson(json["pivot"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        // "pivot": pivot?.toJson(),
      };
}

class ColorPivot {
  ColorPivot({
    required this.productId,
    required this.colorId,
  });

  final int productId;
  final int colorId;

  factory ColorPivot.fromJson(Map<String, dynamic> json) {
    return ColorPivot(
      productId: json["product_id"] ?? 0,
      colorId: json["color_id"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "color_id": colorId,
      };
}

class ImageModel {
  ImageModel({
    required this.id,
    // required this.productId,
    // required this.image,
    required this.isPrimary,
    // required this.createdAt,
    // required this.updatedAt,
    required this.imageUrl,
  });

  final int id;

  // final int productId;
  // final String image;
  final int isPrimary;

  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  final String imageUrl;

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json["id"] ?? 0,
      // productId: json["product_id"] ?? 0,
      // image: json["image"] ?? "",
      isPrimary: json["is_primary"] ?? 0,
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      imageUrl: json["image_url"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        // "product_id": productId,
        // "image": image,
        "is_primary": isPrimary,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        "image_url": imageUrl,
      };
}

class Material {
  Material({
    required this.id,
    required this.name,
    // required this.createdAt,
    // required this.updatedAt,
    // required this.pivot,
  });

  final int id;
  final String name;

  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  // final MaterialPivot? pivot;

  factory Material.fromJson(Map<String, dynamic> json) {
    return Material(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      // pivot: json["pivot"] == null ? null : MaterialPivot.fromJson(json["pivot"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        // "pivot": pivot?.toJson(),
      };
}

class MaterialPivot {
  MaterialPivot({
    required this.productId,
    required this.materialId,
  });

  final int productId;
  final int materialId;

  factory MaterialPivot.fromJson(Map<String, dynamic> json) {
    return MaterialPivot(
      productId: json["product_id"] ?? 0,
      materialId: json["material_id"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "material_id": materialId,
      };
}

class Review {
  Review({
    required this.id,
    required this.productId,
    required this.userId,
    required this.rating,
    required this.comment,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  final int id;
  final int productId;
  final int userId;
  final int rating;
  final dynamic comment;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final User? user;

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json["id"] ?? 0,
      productId: json["product_id"] ?? 0,
      userId: json["user_id"] ?? 0,
      rating: json["rating"] ?? 0,
      comment: json["comment"],
      status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      user: json["user"] == null ? null : User.fromJson(json["user"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "product_id": productId,
        "user_id": userId,
        "rating": rating,
        "comment": comment,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "user": user?.toJson(),
      };
}

class User {
  User({
    required this.id,
    required this.name,
    // required this.email,
    // required this.emailVerifiedAt,
    // required this.phone,
    // required this.photo,
    // required this.role,
    // required this.status,
    // required this.createdAt,
    // required this.updatedAt,
    required this.photoUrl,
  });

  final int id;
  final String name;

  // final String email;
  // final dynamic emailVerifiedAt;
  // final String phone;
  // final String photo;
  // final String role;
  // final String status;
  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  final String photoUrl;

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      // email: json["email"] ?? "",
      // emailVerifiedAt: json["email_verified_at"],
      // phone: json["phone"] ?? "",
      // photo: json["photo"] ?? "",
      // role: json["role"] ?? "",
      // status: json["status"] ?? "",
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      photoUrl: json["photo_url"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        // "email": email,
        // "email_verified_at": emailVerifiedAt,
        // "phone": phone,
        // "photo": photo,
        // "role": role,
        // "status": status,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        "photo_url": photoUrl,
      };
}

class SizeModel {
  SizeModel({
    required this.id,
    required this.name,
    required this.code,
    // required this.createdAt,
    // required this.updatedAt,
    // required this.pivot,
  });

  final int id;
  final String name;
  final String code;

  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  // final SizePivot? pivot;

  factory SizeModel.fromJson(Map<String, dynamic> json) {
    return SizeModel(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      code: json["code"] ?? "",
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      // pivot: json["pivot"] == null ? null : SizePivot.fromJson(json["pivot"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        // "created_at": createdAt?.toIso8601String(),
        // "updated_at": updatedAt?.toIso8601String(),
        // "pivot": pivot?.toJson(),
      };
}

class SizePivot {
  SizePivot({
    required this.productId,
    required this.sizeId,
  });

  final int productId;
  final int sizeId;

  factory SizePivot.fromJson(Map<String, dynamic> json) {
    return SizePivot(
      productId: json["product_id"] ?? 0,
      sizeId: json["size_id"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "size_id": sizeId,
      };
}
