// import 'package:ar_flutter_plugin_updated/datatypes/config_planedetection.dart';
// import 'package:ar_flutter_plugin_updated/datatypes/node_types.dart';
// import 'package:ar_flutter_plugin_updated/models/ar_node.dart';
import 'package:flutter/material.dart';
// import 'package:ar_flutter_plugin_updated/ar_flutter_plugin.dart';
// import 'package:ar_flutter_plugin_updated/managers/ar_object_manager.dart';
// import 'package:ar_flutter_plugin_updated/managers/ar_session_manager.dart';
// import 'package:ar_flutter_plugin_updated/widgets/ar_view.dart';

// class ARViewScreen extends StatefulWidget {
//   const ARViewScreen({super.key});
//
//   @override
//   State<ARViewScreen> createState() => _ARViewScreenState();
// }
//
// class _ARViewScreenState extends State<ARViewScreen> {
//   // ARSessionManager? _arSessionManager;
//   // ARObjectManager? _arObjectManager;
//
//   @override
//   void dispose() {
//     _arSessionManager?.dispose();
//     super.dispose();
//   }
//
//   // void add3DObject() async {
//   //   final node = ARNode(
//   //     type: NodeType.webGLB,
//   //
//   //     uri: "https://modelviewer.dev/shared-assets/models/Astronaut.glb",
//   //   );
//   //
//   //   bool? result = await _arObjectManager?.addNode(node);
//   //   if (result == true) {
//   //     // print("تمت إضافة المجسم بنجاح ✅");
//   //   } else {
//   //     // print("فشل في إضافة المجسم ❌");
//   //   }
//   // }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: Text("عرض نموذج 3D في AR")),
//       // body: ARView(
//       //   showPlatformType: true,
//       //   onARViewCreated: (ARSessionManager sessionManager,
//       //       ARObjectManager objectManager, _, __) {
//       //     _arSessionManager = sessionManager;
//       //     _arObjectManager = objectManager;
//       //
//       //     add3DObject();
//       //   },
//       //   planeDetectionConfig: PlaneDetectionConfig.vertical,
//       // ),
//     );
//   }
// }
class ARViewScreen extends StatelessWidget {
  const ARViewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(),
      ),
    );
  }
}
