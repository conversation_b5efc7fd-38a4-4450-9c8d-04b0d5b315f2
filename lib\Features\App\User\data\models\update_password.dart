class UpdatePassword {
  final String oldPassword;
  final String newPassword;
  final String passwordConfirmation;

  UpdatePassword(
      {required this.oldPassword,
      required this.newPassword,
      required this.passwordConfirmation});

  factory UpdatePassword.fromJson(Map<String, dynamic> json) {
    return UpdatePassword(
        oldPassword: json['current_password'],
        newPassword: json['password'],
        passwordConfirmation: json['password_confirmation']);
  }

  Map<String, dynamic> toMap() {
    return {
      'current_password': oldPassword,
      'password': newPassword,
      'password_confirmation': passwordConfirmation,
    };
  }
}
