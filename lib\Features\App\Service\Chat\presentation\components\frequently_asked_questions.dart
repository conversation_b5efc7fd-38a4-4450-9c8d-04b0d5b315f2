import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../Cubit/chat_ai_cubit.dart';

import '../../data/Model/send_message.dart';


class FrequentlyAskedQuestions extends StatelessWidget {
  const FrequentlyAskedQuestions({
    super.key,
    required this.chatAiCubit,
    required this.locale, required this.state,
  });

  final ChatAiCubit chatAiCubit;
  final ChatAiStateModel state;
  final String locale;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 60.h,
        child: ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: state.faqs!.length,
            itemBuilder: (context, index) {
              final faq = state.faqs![index];
              return SizedBox(
                child: ActionChip(
                  label: Text(faq.question),
                  onPressed: () {
                    chatAiCubit.sendMessage(SendMessage(
                      message: faq.question,
                      lang: locale,
                    ));
                  },
                ),
              );
            }));
  }
}
