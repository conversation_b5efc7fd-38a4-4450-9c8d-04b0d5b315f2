import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';

import '../../data/Models/login_model.dart';
import '../../data/Models/register_model.dart';
import '../../data/Models/user_data_model.dart';

import '../../data/repositories/authentication.dart';
import '../../data/sources/authentication_sources.dart';

class AuthenticationRepoImpl extends AuthenticationRepo {
  @override
  Future<Either<Failure, UserDataModel>> loginByEmail(LoginModel loginModel) async {
    try {
      final response = await AuthenticationSources.getUserDataByEmail(loginModel);

      if (response.status && response.data != null) {
        return right(response.data!);
      } else {
        return left(ServerFailure(response.message));
      }
    } on DioException catch (e) {
      return left(ServerFailure.fromDioError(e));
    } catch (e) {
      return left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> registerByEmail(RegisterModel registerModel) async {
    try {
      final response = await AuthenticationSources.createAccount(registerModel);

      if (response.status) {
        return right(true);
      } else {
        return left(ServerFailure(response.message));
      }
    } on DioException catch (e) {
      return left(ServerFailure.fromDioError(e));
    } catch (e) {
      return left(ServerFailure(e.toString()));
    }
  }
}
