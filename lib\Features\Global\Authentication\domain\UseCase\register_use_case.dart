import 'package:dartz/dartz.dart';
import '../../data/Models/register_model.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repository/authentication_repo_impl.dart';

class RegisterUseCase {
  final AuthenticationRepoImpl authRepositoryImpl;

  RegisterUseCase(this.authRepositoryImpl);

  Future<Either<Failure, bool>> call(RegisterModel registerModel) async =>
      await authRepositoryImpl.registerByEmail(registerModel);
}
