import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../Cubit/AnimatedCrossFade/animated_cross_fade_cubit.dart';
import '../pages/login_page.dart';
import '../pages/signup_page.dart';

class SwitchPagesAuthentications extends StatelessWidget {
  const SwitchPagesAuthentications({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnimatedCrossFadeCubit, CrossFadeStateModel>(
      builder: (context, state) {
        return AnimatedCrossFade(
          firstChild: LoginPage(),
          secondChild: SignUpPage(),
          crossFadeState: context.watch<AnimatedCrossFadeCubit>().state.mode,
          duration: Duration(milliseconds: 600),
          reverseDuration: Duration(milliseconds: 600),
        );
      },
    );
  }
}
