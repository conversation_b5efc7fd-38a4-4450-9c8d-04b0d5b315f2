class UpdateUser {
  String? name;
  String? email;
  String? phone;
  String? image;

  UpdateUser(
    this.name,
    this.email,
    this.phone,
    this.image,
  );

  factory UpdateUser.fromMap(Map<String, dynamic> map) => UpdateUser(
        map['name']?.toString(),
        map['email']?.toString(),
        map['phone']?.toString(),
        map['image']?.toString(),
      );

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'phone': phone,
      'image': image,
    };
  }
}
