import 'package:dartz/dartz.dart';

import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repositories/home_repo_impl.dart';

class GetPopularUseCase {
  final HomeRepoImpl homeRepoImpl;

  GetPopularUseCase(this.homeRepoImpl);

  Future<Either<Failure, List<ReviewProductsModel>>> call() async =>
      await homeRepoImpl.getProductIsPopular();
}

