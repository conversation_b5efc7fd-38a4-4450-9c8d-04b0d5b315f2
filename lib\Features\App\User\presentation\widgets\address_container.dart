import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Resources/app_icons.dart';
import '../../data/models/add_address_model.dart';
import '../Cubit/user_cubit.dart';
import '../../../../../Core/Resources/app_colors.dart';

class AddressContainer extends StatelessWidget {
  final String address;
  final AddressModel model;
  final bool isDefault;

  const AddressContainer({
    super.key,
    required this.address,
    this.isDefault = false,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.0),
      margin: EdgeInsets.symmetric(vertical: 10.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(100),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3), // changes position of shadow
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          context.read<UserCubit>().setDefaultAddress(model);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                address,
                style: TextStyle(fontSize: 16.0),
              ),
            ),
            if (isDefault) AppIcons.check,
            if (!isDefault)
              Container(
                width: 24.w,
                height: 24.h,
                decoration: BoxDecoration(
                    color: Colors.transparent,
                    border: Border.all(color: AppColors.primaryColorDark),
                    shape: BoxShape.circle),
              )
          ],
        ),
      ),
    );
  }
}
