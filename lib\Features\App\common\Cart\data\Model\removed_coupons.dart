class RemovedCouponsModel {
  RemovedCouponsModel({
    required this.removedCoupon,
    required this.removedDiscount,
    required this.summary,
  });

  final dynamic removedCoupon;
  final int removedDiscount;
  final Summary? summary;

  factory RemovedCouponsModel.fromJson(Map<String, dynamic> json){
  return RemovedCouponsModel(
  removedCoupon: json["removed_coupon"],
  removedDiscount: json["removed_discount"] ?? 0,
  summary: json["summary"] == null ? null : Summary.fromJson(json["summary"]),
  );
  }

  Map<String, dynamic> toJson() => {
    "removed_coupon": removedCoupon,
    "removed_discount": removedDiscount,
    "summary": summary?.toJson(),
  };

}

class Summary {
  Summary({
    required this.subtotal,
    required this.couponDiscount,
    required this.deliveryCharge,
    required this.grandTotal,
  });

  final int subtotal;
  final int couponDiscount;
  final int deliveryCharge;
  final int grandTotal;

  factory Summary.fromJson(Map<String, dynamic> json){
    return Summary(
      subtotal: json["subtotal"] ?? 0,
      couponDiscount: json["coupon_discount"] ?? 0,
      deliveryCharge: json["delivery_charge"] ?? 0,
      grandTotal: json["grand_total"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    "subtotal": subtotal,
    "coupon_discount": couponDiscount,
    "delivery_charge": deliveryCharge,
    "grand_total": grandTotal,
  };

}
