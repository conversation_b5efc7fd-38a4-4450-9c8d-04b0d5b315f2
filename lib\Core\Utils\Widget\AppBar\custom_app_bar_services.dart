import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../Resources/app_fonts.dart';
import '../../../../../../../Core/Resources/app_colors.dart';
class CustomAppBarServices extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final void Function() onPressed ;
  const CustomAppBarServices({super.key, required this.title, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      toolbarHeight: 100.h,
      backgroundColor: AppColors.secondaryColor,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed:onPressed,
      ),
      title: Text(
        title,
        style: AppTextStyles.h4Bold.copyWith(color: AppColors.textColorWhite),
      ),
      centerTitle: true,
      flexibleSpace: Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          height: 50,
          width: 1.sw,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(100).r,
            ),
          ),
        ),
      ),


    );
  }

  @override
  Size get preferredSize => Size.fromHeight(150.h);
}