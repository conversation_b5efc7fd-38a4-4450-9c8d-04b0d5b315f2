import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../manager/search_cubit.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_icons.dart';

class CustomSearchField extends StatefulWidget {
  const CustomSearchField({super.key});

  @override
  State<CustomSearchField> createState() => _CustomSearchFieldState();
}

class _CustomSearchFieldState extends State<CustomSearchField> {
  late TextEditingController searchController;

  @override
  void initState() {
    searchController = TextEditingController(text: "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 35, bottom: 24).w,
      child: SearchBar(
        controller: searchController,
        hintText: context.local.Search,
        onChanged: (result) {
          context.read<SearchCubit>().searchByName(name: result);
        },
        leading: IconButton(
          onPressed: () {},
          icon: Opacity(
            opacity: .5,
            child: AppIcons.searchOutline,
          ),
        ),
        // trailing: [
        //   SvgPicture.asset(
        //     AppImagesSvg.vector,
        //     fit: BoxFit.contain,
        //     height: 18.w,
        //   ),
        //   8.horizontalSpace,
        //   InkWell(
        //     onTap: () {
        //       context.buildCustomBottomSheet(
        //           widget: FilterSection(), increaseHeight: true);
        //     },
        //     child: SvgPicture.asset(
        //       AppImagesSvg.filter,
        //       fit: BoxFit.contain,
        //       width: 26.w,
        //     ),
        //   ),
        //   8.horizontalSpace,
        // ],
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.r),
            side: BorderSide(color: AppColors.line),
          ),
        ),
        backgroundColor: WidgetStateProperty.all(AppColors.backgroundColor),
      ),
    );
  }
}
