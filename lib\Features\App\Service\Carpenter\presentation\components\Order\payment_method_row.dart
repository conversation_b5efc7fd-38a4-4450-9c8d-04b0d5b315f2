import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../Cubit/CarpenterService/carpenter_service_cubit.dart';

import '../../../../../../../Core/Utils/Enums/enum_payments.dart';

class PaymentMethodRow extends StatelessWidget {
  const PaymentMethodRow({super.key});

  @override
  Widget build(BuildContext context) {
    final cubitRead = context.read<CarpenterServiceCubit>();
    final cubitWatch = context.watch<CarpenterServiceCubit>().state;
    return Row(
      children: [
        Expanded(
          child: _buildPaymentMethod(
            icon: Icons.credit_card,
            title: 'Online Payment',
            isSelected:
                cubitWatch.paymentMethod == PaymentMethodEnum.creditCard,
            onTap: () =>
                cubitRead.chosePaymentMethod(PaymentMethodEnum.creditCard),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPaymentMethod(
            icon: Icons.wallet,
            title: 'Cash',
            isSelected: cubitWatch.paymentMethod == PaymentMethodEnum.cash,
            onTap: () => cubitRead.chosePaymentMethod(PaymentMethodEnum.cash),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethod({
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.0),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: isSelected ? Colors.orange : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Stack(
          children: [
            Align(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 32, color: Colors.grey.shade600),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            )),
            if (isSelected)
              const CircleAvatar(
                radius: 8,
                backgroundColor: Colors.orange,
                child: Icon(Icons.check, size: 10, color: Colors.white),
              ),
          ],
        ),
      ),
    );
  }
}
