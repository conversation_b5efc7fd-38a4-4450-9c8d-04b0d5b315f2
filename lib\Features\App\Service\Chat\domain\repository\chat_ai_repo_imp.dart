 import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

import '../../data/Model/faqs_model.dart';

import '../../data/Model/message.dart';

import '../../data/Model/send_message.dart';

import '../../data/Model/view_messages.dart';

import '../../data/repositories/chat_ai_repo.dart';
import '../../data/sources/chat_ai_sources.dart';

class ChatAiRepoImp extends ChatAIRepo{
  @override
  Future<Either<Failure, List<FaqsModel>>> getFaqs() async{
    try {
      final response = await ChatAiSources.getChatAiFaqs();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, ViewMessages>> getMessages() async{
    try {
      final response = await ChatAiSources.getChatAiMessage();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, List<MessageModel>>> sendMessage({required SendMessage sendMessage}) async{
    try {
      final response = await ChatAiSources.sendChatAiMessage(sendMessage: sendMessage);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }
}