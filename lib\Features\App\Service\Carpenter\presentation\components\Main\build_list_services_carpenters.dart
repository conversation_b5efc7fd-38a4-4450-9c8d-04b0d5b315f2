import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../../../Core/Utils/Extensions/context_extension.dart';
import '../../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../../Core/Utils/Widget/Animations/build_animatedview_list_box.dart';
import '../../../../../../../Core/Resources/app_icons.dart';
import '../../Cubit/CarpenterService/carpenter_service_cubit.dart';
import '../../../data/Models/get_all_services_model.dart';

class BuildListServicesCarpenters extends StatelessWidget {
  const BuildListServicesCarpenters({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    GetAllServicesModel service = GetAllServicesModel.empty();
    List<GetAllServicesModel> services = context.read<CarpenterServiceCubit>().state.services ?? [];
    bool isLoading =
        context.watch<CarpenterServiceCubit>().state.loading ?? true;
    return SizedBox(
            height: 170.h,
            child: isLoading
                ? _buildLoadingServices(context, 0, service)
                : ListView.separated(
                    scrollDirection: Axis.horizontal,
                    itemCount: services.length,
                    separatorBuilder: (context, index) => 10.horizontalSpace,
                    itemBuilder: (context, index) {
                      return _buildServices(
                          context, isLoading, index, services);
                    },
                  ),
          );
  }

  Widget _buildServices(BuildContext context, bool isLoading, int index,
      List<GetAllServicesModel> services) {

    return BuildAnimatedviewListBox(index: index, child: Skeletonizer(
      enabled: isLoading,
      child: InkWell(
        onTap: () {
          services.isEmpty == true
              ? null
              : context.read<CarpenterServiceCubit>().selectedService(services[index].id);
        },
        onDoubleTap: () {
          services.isEmpty == true
              ? null
              : context.buildCustomBottomSheet(
                  widget: Column(
                  children: [
                    Center(
                      child: Text(services[index].name),
                    )
                  ],
                ));
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadiusDirectional.circular(15.r),
            boxShadow: [
              BoxShadow(
                color: context
                            .watch<CarpenterServiceCubit>()
                            .state
                            .selectedService ==
                    services[index].id
                    ? AppColors.primaryColor.withAlpha(200)
                    : Colors.transparent,
                spreadRadius: 1,
              )
            ],
          ),
          child: Column(

            children: [
              Container(
                width: 140.w,
                height: 130.h,
                margin: EdgeInsets.all(5).r,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.r),
                  color: PastelColorGenerator.getColorByIndex(index),
                ),
                child: Center(
                  child: AppIcons.carpenter,
                ),
              ),
              Expanded(
                child: Center(
                  child: Text(services[index].name, style: AppTextStyles.bodyLargeBold,),
                ),
              )
            ],
          ),
        ),
      ),
    ));

  }


  Widget _buildLoadingServices(
      BuildContext context, int index, GetAllServicesModel services) {
    return _buildServices(context, true, index, [services]);
  }
}

class PastelColorGenerator {
  static final List<Color> _pastelColors = [
    Color(0xFFFFF1E6), // Light Peach
    Color(0xFFE6F4FF),
    Color(0xFFF2EBFF),
    Color(0xFFE7FFD9),
    Color(0xFFFFE6E6),
  ];

  static Color? _lastColor;

  static Color getNextColor() {
    List<Color> availableColors =
        _pastelColors.where((c) => c != _lastColor).toList();
    _lastColor = availableColors[Random().nextInt(availableColors.length)];
    return _lastColor!;
  }

  static Color getColorByIndex(int index) {
    int adjustedIndex = index % _pastelColors.length;
    return _pastelColors[adjustedIndex];
  }
}
