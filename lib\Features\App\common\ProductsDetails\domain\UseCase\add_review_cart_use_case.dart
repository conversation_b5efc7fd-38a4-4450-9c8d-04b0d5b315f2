import 'package:dartz/dartz.dart';
import '../../data/entities/product_details_model.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repository/product_details_repo_impl.dart';

class AddReviewCartUseCase {
  final ProductDetailsRepoImpl productDetailsRepoImpl;

  AddReviewCartUseCase({required this.productDetailsRepoImpl});

  Future<Either<Failure, Review>> call({required int rating,  String? comment, required int productId}) async {
    return await productDetailsRepoImpl.addReview(
        rating: rating, comment: comment, productId: productId
    );
  }

}
