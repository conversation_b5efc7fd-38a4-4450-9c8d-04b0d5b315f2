part of 'chat_ai_cubit.dart';

@immutable
class ChatAiStateModel {
  final String? message;
  final bool? isLoading;
  final bool? isShow;
  final bool? isError;
  final bool? isTyping;
  final bool? isAuth;
  final List<MessageModel>? messages;
  final List<FaqsModel>? faqs;

  const ChatAiStateModel({
    this.message,
    this.isLoading,
    this.isShow,
    this.isError,
    this.isTyping,
    this.isAuth,
    this.messages = const [],
    this.faqs = const [],
  });

  ChatAiStateModel copyWith({
    final String? message,
    final bool? isLoading,
    final bool? isShow,
    final bool? isError,
    final bool? isTyping,
    final bool? isAuth,
    final List<MessageModel>? messages,
    final List<FaqsModel>? faqs,
  }) {
    return ChatAiStateModel(
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      isShow: isShow ?? this.isShow,
      isError: isError ?? this.isError,
      isTyping: isTyping ?? this.isTyping,
      isAuth: isAuth ?? this.isAuth,
      messages: messages ?? this.messages,
      faqs: faqs ?? this.faqs,
    );
  }
}