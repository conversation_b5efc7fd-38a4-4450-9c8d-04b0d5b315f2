import '../Model/send_message.dart';

import '../../../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../../../Core/Storage/Remote/api_service.dart';
import '../Model/faqs_model.dart';
import '../Model/message.dart';
import '../Model/view_messages.dart';

class ChatAiSources {

  static Future<List<MessageModel>> sendChatAiMessage(
      { required SendMessage sendMessage}) async {
    final response = await DioHelper.postData(
      data: sendMessage.toMap(),
      path: ApiEndpoints.chatAiSend,
    );
    final data = List<MessageModel>.from(response.data["data"]["messages"].map((x) => MessageModel.fromJson(x)));
    return data;
  }

  static Future<ViewMessages> getChatAiMessage() async {
    final response = await DioHelper.getData(
      path: ApiEndpoints.chatAiGet,
    );
    final data = ViewMessages.fromJson(response.data["data"]);
    return data;
  }

  static Future<List<FaqsModel>> getChatAiFaqs() async {
    final response = await DioHelper.getData(
      path: ApiEndpoints.chatAiFaqs,
    );
    final data = List<FaqsModel>.from(response.data["data"]["faqs"].map((x) => FaqsModel.fromJson(x)));
    return data;
  }

}
