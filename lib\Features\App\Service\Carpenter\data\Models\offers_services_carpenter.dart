import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../Config/Assets/image_svg.dart';
import '../../../../../../Core/Utils/Widget/Images/build_image.dart';

class OffersModel {
  final int id;
  final String name;
  final String discount;
  final String description;
  OffersModel( {
  required  this.name,
  required  this.discount,
  required this.description,
  required this.id,
  });
static Widget  image(int index) {
    List<String> img = [
      AppImagesSvg.cardOffersCarpenters,
      AppImagesSvg.cardOffersCarpenters1,
      AppImagesSvg.cardOffersCarpenters2,
    ];
    return BuildImageAssets(svg: img[index],width:  359.w,);
  }

}
