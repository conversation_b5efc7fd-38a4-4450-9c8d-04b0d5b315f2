import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final screenWidth = size.width;
    final screenHeight = size.height;

    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color.fromRGBO(255, 255, 255, 0),
        Color.fromRGBO(255, 255, 255, 0.3),
        Color.fromRGBO(255, 255, 255, 0),
      ],
      stops: const [0.0, 1.0, 0.0],
    );

    final paintStroke = Paint()
      ..shader =
          gradient.createShader(Rect.fromLTWH(0, 0, screenWidth, screenHeight))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 50.w
      ..strokeCap = StrokeCap.round
      ..isAntiAlias = true;

    final paintFill = Paint()
      ..shader =
          gradient.createShader(Rect.fromLTWH(0, 0, screenWidth, screenHeight))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    canvas.drawCircle(
      Offset(screenWidth, 0),
      screenWidth * 0.5,
      paintFill,
    );
    //.35-.5=0.15
    // .65-.35=0.3
    canvas.drawCircle(
      Offset(screenWidth , screenHeight * -0.02),
      screenWidth * 0.65,
      paintStroke,
    );

    canvas.drawCircle(
      Offset(screenWidth , screenHeight * -0.03),
      screenWidth * 0.95,
      paintStroke,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
