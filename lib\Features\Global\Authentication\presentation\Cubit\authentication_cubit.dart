import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../App/User/presentation/Cubit/user_cubit.dart';
import '../../../../../Core/Utils/Widget/Massages/custom_scaffold_messenger.dart';
import '../../domain/UseCase/login_use_case.dart';
import '../../../../../Config/Routes/route_name.dart';
import '../../../../../main.dart';
import '../../data/Models/register_model.dart';
import '../../data/Models/user_data_model.dart';

import '../../data/Models/login_model.dart';
import '../../domain/UseCase/register_use_case.dart';

part 'authentication_state.dart';

class AuthenticationCubit extends Cubit<AuthenticationState> {
  AuthenticationCubit(
    this.login,
    this.register,
  ) : super(
          AuthenticationInitial(),
        );

  final LoginUseCase login;
  final RegisterUseCase register;

  Future<void> loginByEmail(
      BuildContext context, LoginModel loginModel, String local) async {
    emit(AuthenticationLoginLoading());

    final response = await login.call(
      loginModel,
    );
    response.fold((failure) async {
      showCustomSnackBar(context, failure.errMessage, SnackBarType.error);
      emit(AuthenticationLoginError(message: failure.errMessage));
    }, (data) async {
      // showCustomSnackBar(context, local, SnackBarType.success);
      context.read<UserCubit>().getUserData();
      context.read<UserCubit>().getAddress();
      await LocalStorageService.setValue(LocalStorageKeys.isFirstTime, true);
      emit(AuthenticationLoginSuccess(data: data));
      await kNavigationService.clearAndNavigateTo(AppRoutes.main);
    });
  }

  Future<void> createAccount(
      RegisterModel registerModel, String local, BuildContext context) async {
    emit(AuthenticationCreateAccountLoading());

    final response = await register.call(registerModel);
    response.fold((failure) {
      emit(AuthenticationCreateAccountError(message: failure.errMessage));
      showCustomSnackBar(context, failure.errMessage, SnackBarType.error);
    }, (data) {
      showCustomSnackBar(context, local, SnackBarType.success);
      emit(AuthenticationCreateAccountSuccess());
    });
  }

  void handleUnauthorizedAccess() {
    kNavigationService.clearAndNavigateTo(AppRoutes.authentication);
  }
}
