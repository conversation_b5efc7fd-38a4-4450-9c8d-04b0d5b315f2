import 'package:dartz/dartz.dart';
import '../../data/Model/message.dart';
import '../../data/Model/send_message.dart';
import '../repository/chat_ai_repo_imp.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

class SendMessagesUseCase {
  final ChatAiRepoImp repo;

  SendMessagesUseCase(this.repo);

  Future<Either<Failure, List<MessageModel>>> call(SendMessage sendMessage) => repo.sendMessage(sendMessage: sendMessage);

}
