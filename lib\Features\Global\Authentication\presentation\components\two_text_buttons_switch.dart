import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_fonts.dart';
import '../Cubit/AnimatedCrossFade/animated_cross_fade_cubit.dart';

class TwoTextButtonsSwitch extends StatelessWidget {
  final String firstText;
  final String secondText;

  const TwoTextButtonsSwitch({
    super.key,
    required this.firstText,
    required this.secondText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0, bottom: 10).w,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(firstText, style: AppTextStyles.bodyMediumBold),
          3.horizontalSpace,
          GestureDetector(
            onTap: () async {
              await context.read<AnimatedCrossFadeCubit>().tapChange();
            },
            child: Text(
              secondText,
              style: AppTextStyles.bodyMediumBold.copyWith(
                color: AppColors.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
