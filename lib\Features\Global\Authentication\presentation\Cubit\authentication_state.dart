part of 'authentication_cubit.dart';

@immutable
sealed class AuthenticationState {}

final class AuthenticationInitial extends AuthenticationState {}

final class AuthenticationLoginLoading extends AuthenticationState {}

final class AuthenticationLoginSuc<PERSON> extends AuthenticationState {
  final UserDataModel? data;

  AuthenticationLoginSuccess({this.data});
}

final class AuthenticationLoginError extends AuthenticationState {
  final String message;

  AuthenticationLoginError({required this.message});
}

//* CreateAccount
final class AuthenticationCreateAccountLoading extends AuthenticationState {}

final class AuthenticationCreateAccountSuccess extends AuthenticationState {}

final class AuthenticationCreateAccountError extends AuthenticationState {
  final String message;

  AuthenticationCreateAccountError({required this.message});
}
