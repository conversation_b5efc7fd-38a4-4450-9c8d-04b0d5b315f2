import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';
import '../../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../../Config/Assets/image_png.dart';

import '../../../../../../../Core/Services/Map/Static/custom_icon_maps.dart';
import '../../../../../../../Core/Services/Map/Static/location_service.dart';
import '../../../../../../../Core/Services/Map/Static/markers_model.dart';
import '../../Cubit/CarpenterService/carpenter_service_cubit.dart';
import 'carpenter_details.dart';

class MapSample extends StatefulWidget {

  const MapSample({super.key});

  @override
  State<MapSample> createState() => MapSampleState();
}

class MapSampleState extends State<MapSample> {
  static const CameraPosition _kGooglePlex = CameraPosition(
    target: LatLng(27.669593268876408, 30.789149139355608), // point of the map
    zoom: 6,
  );
  final Set<Marker> _markers = {};
  late LocationService locationService;

  // world view O -> 1
  // country view 4 -> 6
  // city view 10 -> 12
  // street  view  13 -> 17
  // building view 18 -> 20
  GoogleMapController? mapController;

  @override
  void initState() {
    locationService = LocationService();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GoogleMap(
        markers: _markers,
        buildingsEnabled: false,
        mapType: MapType.terrain,
        initialCameraPosition: _kGooglePlex,
        onMapCreated: (GoogleMapController controller)async {
          mapController = controller;
         await setMyLocation();

        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primaryColor,
        onPressed: () async {
          await setMyLocation();
        },
        child: const Icon(
          Icons.location_on,
          color: Colors.white,
      ),
    ));
  }

  Future<void> setMyLocation() async {
    try {
      var data = await locationService.getLocation();

      LatLng currentLocation = LatLng(data.latitude!, data.longitude!);
      MarkersModel.lat = data.latitude!;
      MarkersModel.lng = data.longitude!;
      final myMarker = await CustomIconMaps.getImageFromRawData(
          AppImagesPng.markerLocation, 100);
      var marker = Marker(
          markerId: MarkerId('MyLocation'),
          position: currentLocation,
          icon: BitmapDescriptor.bytes(myMarker)
      );
      setState(() {
        _markers.add(marker);
      });
      mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: currentLocation, zoom: 17),
        ),
      );
      await initMarkers();
    } on LocationServiceException {
        // print("Location Service Exception");
    } on LocationPermissionException {
      // print("Location Permission Exception");
    } catch (e) {
      // print("Unknown Exception");
    }
  }


  Future<void> initMarkers() async {
    final cubit = context
        .read<CarpenterServiceCubit>()
        .state;
    final  assetName = await CustomIconMaps.getImageFromRawData(
        AppImagesPng.carpenter, 80);

    final icon = BitmapDescriptor.bytes(assetName);
    final value = cubit.carpenters!
        .map(
          (e) {
        return Marker(
          infoWindow: InfoWindow(
            title: e.user!.name,
          ),
          onTap: () => context.read<CarpenterServiceCubit>().changeScreen(CarpenterDetails(model: e,)),
          icon:icon,
          markerId: MarkerId(e.id.toString()),
          position: MarkersModel.generateRandomPoint(),
        );
      },
    )
        .toSet();
    _markers.addAll(value);
    setState(() {});
  }


}
