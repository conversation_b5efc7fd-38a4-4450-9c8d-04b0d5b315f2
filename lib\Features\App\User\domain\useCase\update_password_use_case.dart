import 'package:dartz/dartz.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/models/update_password.dart';
import '../repositories/user_repo_imp.dart';

class UpdatePasswordUseCase {
  final UserRepoImp userRepoImp;

  UpdatePasswordUseCase({required this.userRepoImp});

  Future<Either<Failure, bool>> call(UpdatePassword updatePassword) async =>
      await userRepoImp.updatePassword(updatePassword);
}
