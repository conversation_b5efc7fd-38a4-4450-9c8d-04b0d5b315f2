import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecase/create_orders_use_case.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../domain/repository/carpenter_services_repo_impl.dart';
import '../../domain/usecase/carpenters_use_case.dart';
import 'Body/main_carpenter_services_screen.dart';
import '../../../../../../main.dart';
import '../../../../../../Core/Utils/Widget/AppBar/custom_app_bar_services.dart';
import '../../domain/usecase/carpenter_services_use_case.dart';
import '../Cubit/CarpenterService/carpenter_service_cubit.dart';

class CarpentersScreen extends StatelessWidget {
  const CarpentersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CarpenterServiceCubit(
        CarpenterServicesUseCase(
          CarpenterServicesRepoImpl(),
        ),
        CarpentersUseCase(
          CarpenterServicesRepoImpl(),
        ),
        CreateOrdersUseCase(
          CarpenterServicesRepoImpl(),
        ),
      )..init(),
      child: BlocBuilder<CarpenterServiceCubit, CarpenterServiceState>(
        builder: (context, state) {
          return Scaffold(
            appBar: CustomAppBarServices(
              onPressed: () {
                if (state.screen is MainCarpenterServicesScreen) {
                  kNavigationService.goBack();
                } else {
                  context.read<CarpenterServiceCubit>().goBack();
                }
              },
              title: context.local.Carpenter_service,
            ),
            body: state.screen,
          );
        },
      ),
    );
  }
}
//
