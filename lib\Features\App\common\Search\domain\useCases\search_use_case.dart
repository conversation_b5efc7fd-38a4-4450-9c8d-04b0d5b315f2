import 'package:dartz/dartz.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../repositories/search_repo_impl.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

class SearchUseCase {
  SearchRepoImpl searchRepo;

  SearchUseCase(this.searchRepo);

  Future<Either<Failure, List<ReviewProductsModel>>> call(
     ) async{
    return await searchRepo.searchProducts();
  }
}
