import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../../Storage/Remote/api_error_handler.dart';
import '../../data/repositories/payment_repo.dart';
import '../../service_strip.dart';
import '../entities/payment_input_entities.dart';

class PaymentStripRepoImpl extends PaymentRepo {
  @override
  Future<Either<Failure, bool>> makePaymentIntentStrip(
      {required PaymentInputEntities paymentInputEntities}) async {
    try {
      final StripService stripService = StripService();
      await stripService.makePaymentIntent(
          paymentInputEntities: paymentInputEntities);
      return right(true);
    } catch (e) {
      if (e is DioException) {
        return left(ServerFailure.fromDioError(e));
      } else {
        return Left(ServerFailure("Unknown error occurred"));
      }
    }
  }

}
