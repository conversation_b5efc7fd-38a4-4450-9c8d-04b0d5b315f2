class MessageModel {
  MessageModel({
    required this.id,
    required this.role,
    required this.content,
    required this.createdAt,
  });

  final int id;
  final String role;
  final String content;
  final DateTime? createdAt;

  factory MessageModel.fromJson(Map<String, dynamic> json){
    return MessageModel(
      id: json["id"] ?? 0,
      role: json["role"] ?? "",
      content: json["content"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "role": role,
    "content": content,
    "created_at": createdAt?.toIso8601String(),
  };

}
