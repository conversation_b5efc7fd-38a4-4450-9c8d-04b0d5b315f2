import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../Config/Cubit/settings_cubit.dart';
import '../../Resources/app_colors.dart';
import 'localizations_extension.dart';

extension ContextExtension on BuildContext {
  bool get isSmallScreen => ScreenUtil().screenWidth < 600;

  bool get isMediumScreen => ScreenUtil().screenWidth < 900;

  bool get isInternet => watch<SettingsCubit>().state.internet;

  String getGreeting() {
    final hour = DateTime
        .now()
        .hour;
    if (hour >= 5 && hour < 5) {
      return local.GoodMorning;
    } else {
      return local.GoodNight;
    }
  }

  Future<void> buildCustomBottomSheet({
    Widget? widget,
    WidgetBuilder? widgetBuilder,
    bool increaseHeight = false,
    double? maxHeight ,
  
  }) {
    if (!mounted) return Future.value();
    return showModalBottomSheet(
      showDragHandle: true,
      isScrollControlled: true,
      
      enableDrag: true,
      constraints: BoxConstraints(
        maxHeight:maxHeight ?? (increaseHeight ? 0.8.sh : 0.5.sh),
      ),
      useSafeArea: true,
      backgroundColor: AppColors.backgroundColor,
      context: this,
      builder: widgetBuilder ?? (context) => widget ?? Column(),
    );
  }

}
