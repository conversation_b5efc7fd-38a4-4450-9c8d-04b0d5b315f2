import 'package:flutter/material.dart';
import 'package:introduction_screen/introduction_screen.dart';
import '../../../../Core/Resources/app_colors.dart';
import '../../../Core/Resources/app_constants.dart';
import '../../../Core/Resources/app_list.dart';
import '../../../Core/Storage/Local/local_storage_service.dart';
import '../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../Config/Routes/route_name.dart';
import "../../../main.dart";

import 'widgets/build_button_boarding.dart';

class BoardingScreen extends StatelessWidget {
  const BoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final local = context.local;
    return Scaffold(
      body: IntroductionScreen(
        globalBackgroundColor: AppColors.backgroundColor,
        pages: AppList.buildPagesIntroductionScreen(local),
        showBackButton: true,
        showSkipButton: false,
        showNextButton: true,
        next: buildButtonBoarding( local.Next),
        back: buildButtonBoarding( local.Back),
        dotsDecorator: AppConstants.dotsDecoration,
        done: buildButtonBoarding( local.Continue),
        showDoneButton: true,
        onDone: () async{
          await LocalStorageService.setValue(LocalStorageKeys.isOnboardingCompleted, true);
          kNavigationService.clearAndNavigateTo(AppRoutes.authentication);
        },
      ),
    );
  }
}
