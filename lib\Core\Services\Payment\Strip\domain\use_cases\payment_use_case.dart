import 'package:dartz/dartz.dart';

import '../../../../../Storage/Remote/api_error_handler.dart';
import '../../data/repositories/payment_repo.dart';
import '../entities/payment_input_entities.dart';

class PaymentStripUseCase {
  final PaymentRepo paymentRepo;

  PaymentStripUseCase(this.paymentRepo);

  Future<Either<Failure, bool>> execute(
      {required PaymentInputEntities paymentInputEntities}) async {
    return await paymentRepo.makePaymentIntentStrip(
        paymentInputEntities: paymentInputEntities);

  }
}
//manager