import 'package:dartz/dartz.dart';
import '../../../../Global/Authentication/data/Models/user_data_model.dart';
import '../models/add_address_model.dart';
import '../models/update_password.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../models/update_user.dart';

abstract class UserRepo {
  Future<Either<Failure, UpdateUser>> updateUser(UpdateUser updateUser);
  Future<Either<Failure, bool>> updatePassword(UpdatePassword updatePassword);
  Future<Either<Failure, UserDataModel>> getUserDataModel();
  Future<Either<Failure, UserDataModel>> refreshToken();
  Future<Either<Failure, bool>> logout();

  Future<Either<Failure, List<AddressModel>>> getAddress();
  Future<Either<Failure, AddressModel>> addNewAddress(AddressModel address);
  Future<Either<Failure, AddressModel>> updateAddress(AddressModel address);
  Future<Either<Failure, bool>> removeAddress(AddressModel address);
  Future<Either<Failure, AddressModel>> setDefaultAddress(AddressModel address);

}