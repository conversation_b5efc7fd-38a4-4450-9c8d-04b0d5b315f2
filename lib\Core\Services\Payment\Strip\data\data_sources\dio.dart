import 'package:dio/dio.dart';

import 'api_keys.dart';

class DioHelperPayment {
  static late Dio dio;

  static init() {
    dio = Dio(
      BaseOptions(
        baseUrl: AppEndPoint.baseUrlStripe,
        receiveDataWhenStatusError: true,
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 5),
      ),
    );
    // dio.interceptors.add(LogInterceptor(
    //   request: true,
    //   requestHeader: true,
    //   requestBody: true,
    //   responseHeader: true,
    //   responseBody: true,
    //   error: true,
    // ));
  }

  static Future<Response> getData({
    required String path,
    String? token,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    options ??= Options();
    if (token != null) {
      options.headers = {
        'Authorization': 'Bearer $token',
      };
    }
    return await dio.get(
      path,
      queryParameters: queryParameters,
      options: options,
    );
  }

  static Future<Response> postData({
    required String path,
    required dynamic data,
    String? token,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    options ??= Options(contentType: 'application/json'); //'application/json'
    if (token != null) {
      options.headers = {
        'Authorization': 'Bearer $token',
      };
    }
    return await dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  static Future<Response> putData({
    required String path,
    required Map<String, dynamic> data,
    String? token,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    options ??= Options();
    if (token != null) {
      options.headers = {
        'Authorization': 'Bearer $token',
      };
    }
    return await dio.put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }

  static Future<Response> deleteData({
    required String path,
    String? token,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    options ??= Options();
    if (token != null) {
      options.headers = {
        'Authorization': 'Bearer $token',
      };
    }
    return await dio.delete(
      path,
      queryParameters: queryParameters,
      options: options,
    );
  }
}