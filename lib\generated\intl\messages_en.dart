// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "AddAddress": MessageLookupByLibrary.simpleMessage("Add Address"),
        "AddReview": MessageLookupByLibrary.simpleMessage("Add Review"),
        "AddToCart": MessageLookupByLibrary.simpleMessage("Add to Cart"),
        "Address": MessageLookupByLibrary.simpleMessage("Address"),
        "ApplyFilters": MessageLookupByLibrary.simpleMessage("Apply Filters"),
        "Back": MessageLookupByLibrary.simpleMessage("Back"),
        "Bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "BuyNow": MessageLookupByLibrary.simpleMessage("Buy Now"),
        "CM": MessageLookupByLibrary.simpleMessage("CM"),
        "Camera": MessageLookupByLibrary.simpleMessage("Camera"),
        "Carpenter": MessageLookupByLibrary.simpleMessage("Carpenter"),
        "CarpenterDes": MessageLookupByLibrary.simpleMessage(
            "It can show you what is best for you"),
        "Carpenter_service":
            MessageLookupByLibrary.simpleMessage("Carpenter Service"),
        "Carpentry_selection": MessageLookupByLibrary.simpleMessage(
            "Select the type of Service you need."),
        "Cash": MessageLookupByLibrary.simpleMessage("Cash"),
        "Categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "ChangePassword":
            MessageLookupByLibrary.simpleMessage("Change Password"),
        "Color": MessageLookupByLibrary.simpleMessage("Color"),
        "Confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
        "ConfirmationPassword":
            MessageLookupByLibrary.simpleMessage("Confirmation Password"),
        "Continue": MessageLookupByLibrary.simpleMessage("Continue"),
        "CouponCode": MessageLookupByLibrary.simpleMessage("Coupon Code"),
        "CouponDiscount":
            MessageLookupByLibrary.simpleMessage("Coupon Discount"),
        "CurrentPassword":
            MessageLookupByLibrary.simpleMessage("Current Password"),
        "CustomerInformation":
            MessageLookupByLibrary.simpleMessage("Customer Information"),
        "CustomerReviews":
            MessageLookupByLibrary.simpleMessage("Customer Reviews"),
        "Date": MessageLookupByLibrary.simpleMessage("Date "),
        "DeliveryCharge":
            MessageLookupByLibrary.simpleMessage("Delivery Charge"),
        "Description": MessageLookupByLibrary.simpleMessage("Description"),
        "Details": MessageLookupByLibrary.simpleMessage("Details"),
        "Discount": MessageLookupByLibrary.simpleMessage("Discount"),
        "EmptySpaceCalculatorFeature":
            MessageLookupByLibrary.simpleMessage("Empty Space Calculator"),
        "Enter": MessageLookupByLibrary.simpleMessage("Enter"),
        "EstimatedCost": MessageLookupByLibrary.simpleMessage("Estimated Cost"),
        "Explore": MessageLookupByLibrary.simpleMessage("Explore"),
        "Favourite": MessageLookupByLibrary.simpleMessage("Favorite"),
        "Featured": MessageLookupByLibrary.simpleMessage("Featured"),
        "Filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "FurnitureMeasurementFeature":
            MessageLookupByLibrary.simpleMessage("Furniture Measurement"),
        "FurnitureRecommendationFeature":
            MessageLookupByLibrary.simpleMessage("Furniture Recommendation"),
        "GoodMorning": MessageLookupByLibrary.simpleMessage("Good Morning"),
        "GoodNight": MessageLookupByLibrary.simpleMessage("Good Night"),
        "Home": MessageLookupByLibrary.simpleMessage("Home"),
        "IDCard_Passport": MessageLookupByLibrary.simpleMessage("ID Card"),
        "IDCard_PassportDescription": MessageLookupByLibrary.simpleMessage(
            "Upload clear image of the ID card."),
        "InStock": MessageLookupByLibrary.simpleMessage("In Stock"),
        "Info": MessageLookupByLibrary.simpleMessage("Help & Support"),
        "Languages": MessageLookupByLibrary.simpleMessage("Languages"),
        "LatestProduct":
            MessageLookupByLibrary.simpleMessage("Latest Products"),
        "Length": MessageLookupByLibrary.simpleMessage("Length"),
        "LocationInformation":
            MessageLookupByLibrary.simpleMessage("Location Information"),
        "Login": MessageLookupByLibrary.simpleMessage("Login"),
        "LoginByGoogle":
            MessageLookupByLibrary.simpleMessage("Sign in with Google"),
        "Logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "MessageEmptyProduct":
            MessageLookupByLibrary.simpleMessage("No Product Found"),
        "NewPassword": MessageLookupByLibrary.simpleMessage("New Password"),
        "Next": MessageLookupByLibrary.simpleMessage("Next"),
        "NoImageSelected":
            MessageLookupByLibrary.simpleMessage("No Image Selected"),
        "Note": MessageLookupByLibrary.simpleMessage("Note"),
        "Notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "OnlinePayment": MessageLookupByLibrary.simpleMessage("OnlinePayment"),
        "OrderDetails": MessageLookupByLibrary.simpleMessage("Order Details"),
        "OrderNames": MessageLookupByLibrary.simpleMessage("Order Names"),
        "OrderStatus": MessageLookupByLibrary.simpleMessage("Order Status"),
        "OutStock": MessageLookupByLibrary.simpleMessage("Out of Stock"),
        "Photo": MessageLookupByLibrary.simpleMessage("Photo"),
        "PickTime": MessageLookupByLibrary.simpleMessage("Pick time"),
        "PoliceRecord": MessageLookupByLibrary.simpleMessage("Police Record"),
        "PoliceRecordDescription": MessageLookupByLibrary.simpleMessage(
            "Upload clear image of the police record."),
        "Price": MessageLookupByLibrary.simpleMessage("Price"),
        "Proceed": MessageLookupByLibrary.simpleMessage("Proceed"),
        "Profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "ProfilePhoto": MessageLookupByLibrary.simpleMessage("Profile Photo"),
        "PromoCode": MessageLookupByLibrary.simpleMessage("Promo code"),
        "Quantity": MessageLookupByLibrary.simpleMessage("Quantity"),
        "Recommendation":
            MessageLookupByLibrary.simpleMessage("Service Recommendation"),
        "RecommendationDes": MessageLookupByLibrary.simpleMessage(
            "You can take any picture in your room and get a recommendation on what\'s best for you"),
        "Regular_cost": MessageLookupByLibrary.simpleMessage(
            "Regular cost is 252 EGP/hr. Total cost will be calculated later."),
        "Rent": MessageLookupByLibrary.simpleMessage("Rent"),
        "RentDes": MessageLookupByLibrary.simpleMessage(
            "For rental tools and materials you need"),
        "Required_person":
            MessageLookupByLibrary.simpleMessage("Required Person"),
        "ResetFilters": MessageLookupByLibrary.simpleMessage("Reset Filters"),
        "Reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "RoomMeasurementFeature":
            MessageLookupByLibrary.simpleMessage("Room Measurement"),
        "RoomType": MessageLookupByLibrary.simpleMessage("Room type"),
        "Save": MessageLookupByLibrary.simpleMessage("Save"),
        "Scan": MessageLookupByLibrary.simpleMessage("Scan"),
        "Search": MessageLookupByLibrary.simpleMessage("Search"),
        "Security": MessageLookupByLibrary.simpleMessage("Security"),
        "SeeAll": MessageLookupByLibrary.simpleMessage("See All"),
        "Service": MessageLookupByLibrary.simpleMessage("More Services"),
        "ServiceCharge": MessageLookupByLibrary.simpleMessage("Service Charge"),
        "ServiceDes":
            MessageLookupByLibrary.simpleMessage("Tap here for more services"),
        "ServiceInformation":
            MessageLookupByLibrary.simpleMessage("Service Information"),
        "ServiceName": MessageLookupByLibrary.simpleMessage("Service Name"),
        "Setting": MessageLookupByLibrary.simpleMessage("Settings"),
        "SignUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
        "Size": MessageLookupByLibrary.simpleMessage("Size"),
        "Skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "SmartAssistantFeature":
            MessageLookupByLibrary.simpleMessage("Smart Assistant"),
        "SmartAssistantFeatureDes": MessageLookupByLibrary.simpleMessage(
            "can chat with it and answer any question you have about furniture"),
        "StarRating": MessageLookupByLibrary.simpleMessage("Rating"),
        "Submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "Subtotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
        "Time": MessageLookupByLibrary.simpleMessage("Time"),
        "TitleLogoSplash": MessageLookupByLibrary.simpleMessage(
            "Find the best parts for your home"),
        "TitleStartApp": MessageLookupByLibrary.simpleMessage(
            "Find the perfect furniture for your beautiful home"),
        "TotalAmount": MessageLookupByLibrary.simpleMessage("Total amount"),
        "Trucks": MessageLookupByLibrary.simpleMessage("Trucks"),
        "TrucksDes": MessageLookupByLibrary.simpleMessage(
            "Trucks can take the loads you need to move the furniture"),
        "UploadImage": MessageLookupByLibrary.simpleMessage("Upload Image"),
        "UploadImageCarpenter": MessageLookupByLibrary.simpleMessage(
            "Upload clear image of yourself."),
        "ViewOrders": MessageLookupByLibrary.simpleMessage("View"),
        "Width": MessageLookupByLibrary.simpleMessage("Width"),
        "WorkingPrice": MessageLookupByLibrary.simpleMessage("Working Hour"),
        "Workshop": MessageLookupByLibrary.simpleMessage("Workshop"),
        "WorkshopDashboard":
            MessageLookupByLibrary.simpleMessage("Workshop Dashboard"),
        "WorkshopDescription": MessageLookupByLibrary.simpleMessage(
            "Upload clear image of the workshop."),
        "YourCard": MessageLookupByLibrary.simpleMessage("Your Card"),
        "YourRating": MessageLookupByLibrary.simpleMessage("Your Rating"),
        "accept": MessageLookupByLibrary.simpleMessage("Accept"),
        "accepted": MessageLookupByLibrary.simpleMessage("Finshed"),
        "address_line": MessageLookupByLibrary.simpleMessage("Address Line"),
        "age": MessageLookupByLibrary.simpleMessage("Age"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "area": MessageLookupByLibrary.simpleMessage("Area"),
        "available": MessageLookupByLibrary.simpleMessage("Is Available"),
        "average_rating":
            MessageLookupByLibrary.simpleMessage("Average Rating"),
        "back_confirmation": MessageLookupByLibrary.simpleMessage("Leave Page"),
        "back_confirmation_message": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to leave this page? Any unsaved changes will be lost."),
        "boarding1_body": MessageLookupByLibrary.simpleMessage(
            "Always in a large and diverse selection of options. Guaranteed items, great design."),
        "boarding1_title":
            MessageLookupByLibrary.simpleMessage("Guaranteed quality products"),
        "boarding2_body": MessageLookupByLibrary.simpleMessage(
            "Always in a large and diverse selection of options. Guaranteed items, great design."),
        "boarding2_title": MessageLookupByLibrary.simpleMessage(
            "Comprehensive guarantee if the product doesn\'t suit you"),
        "boarding3_body": MessageLookupByLibrary.simpleMessage(
            "This text is an example of text that can be replaced in the same space."),
        "boarding3_title": MessageLookupByLibrary.simpleMessage(
            "Let\'s achieve your housing needs together"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cart": MessageLookupByLibrary.simpleMessage("Cart"),
        "checkout": MessageLookupByLibrary.simpleMessage("Check it out >"),
        "city": MessageLookupByLibrary.simpleMessage("City"),
        "completed_orders":
            MessageLookupByLibrary.simpleMessage("Completed Orders"),
        "completed_services":
            MessageLookupByLibrary.simpleMessage("Completed Services"),
        "confirmation_dialogs":
            MessageLookupByLibrary.simpleMessage("Confirmation Dialogs"),
        "couponDiscount":
            MessageLookupByLibrary.simpleMessage("Coupon Discount"),
        "delay": MessageLookupByLibrary.simpleMessage("Delay"),
        "delete": MessageLookupByLibrary.simpleMessage("Delete"),
        "delete_all_item_confirmation":
            MessageLookupByLibrary.simpleMessage("Delete all Items "),
        "delete_all_item_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                " Are you sure you want to delete all items ? "),
        "delete_item_confirmation":
            MessageLookupByLibrary.simpleMessage("Delete Item Confirmation"),
        "delete_item_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to delete this item?"),
        "dialog_examples":
            MessageLookupByLibrary.simpleMessage("Dialog Examples"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "error_dialog": MessageLookupByLibrary.simpleMessage("Error Dialog"),
        "error_message": MessageLookupByLibrary.simpleMessage(
            "Something went wrong. Please try again."),
        "experience": MessageLookupByLibrary.simpleMessage("Experience"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("Forgot your password?"),
        "hour": MessageLookupByLibrary.simpleMessage("One Hour"),
        "hourly_rate": MessageLookupByLibrary.simpleMessage("Hourly Rate"),
        "id_card_photo_url":
            MessageLookupByLibrary.simpleMessage("ID Card Photo"),
        "info_saved": MessageLookupByLibrary.simpleMessage("Information saved"),
        "item_deleted": MessageLookupByLibrary.simpleMessage("Item deleted"),
        "later": MessageLookupByLibrary.simpleMessage("Later"),
        "leave": MessageLookupByLibrary.simpleMessage("Leave"),
        "loading_dialog":
            MessageLookupByLibrary.simpleMessage("Loading Dialog"),
        "loading_dialog_title":
            MessageLookupByLibrary.simpleMessage("Loading..."),
        "logout_confirmation":
            MessageLookupByLibrary.simpleMessage("Logout Confirmation"),
        "logout_confirmation_message": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to logout?"),
        "massageInCart":
            MessageLookupByLibrary.simpleMessage("Sorry! No items here yet."),
        "messageChangeLanguageSuccess": MessageLookupByLibrary.simpleMessage(
            "Language changed successfully"),
        "messageLoginSuccess":
            MessageLookupByLibrary.simpleMessage("Logged in successfully"),
        "messageLogoutSuccess":
            MessageLookupByLibrary.simpleMessage("Logged out successfully"),
        "messageRegisterSuccess":
            MessageLookupByLibrary.simpleMessage("Registered successfully"),
        "monthly_sales": MessageLookupByLibrary.simpleMessage("Monthly Sales"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "navigating_back":
            MessageLookupByLibrary.simpleMessage("Navigating back"),
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "no_pending_orders":
            MessageLookupByLibrary.simpleMessage("No Pending Orders"),
        "no_today_orders":
            MessageLookupByLibrary.simpleMessage("No Today Orders"),
        "offerPrice": MessageLookupByLibrary.simpleMessage("Off"),
        "offerTittle":
            MessageLookupByLibrary.simpleMessage("Top Quality Products"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "operation_completed":
            MessageLookupByLibrary.simpleMessage("Operation completed"),
        "orLogin":
            MessageLookupByLibrary.simpleMessage("Already have an account?"),
        "orSingUp":
            MessageLookupByLibrary.simpleMessage("Don\'t have an account?"),
        "order_submitted":
            MessageLookupByLibrary.simpleMessage("Order submitted"),
        "orders": MessageLookupByLibrary.simpleMessage("Orders"),
        "password": MessageLookupByLibrary.simpleMessage("Password"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "pending_orders":
            MessageLookupByLibrary.simpleMessage("Pending Orders"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "please_wait": MessageLookupByLibrary.simpleMessage("Please wait..."),
        "police_record_photo_url":
            MessageLookupByLibrary.simpleMessage("Police Record Photo"),
        "popular": MessageLookupByLibrary.simpleMessage("Popular"),
        "postal_code": MessageLookupByLibrary.simpleMessage("Postal Code"),
        "profile_status":
            MessageLookupByLibrary.simpleMessage("Profile Status"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "reject": MessageLookupByLibrary.simpleMessage("Reject"),
        "running": MessageLookupByLibrary.simpleMessage("Running"),
        "save_info_confirmation": MessageLookupByLibrary.simpleMessage(
            "Save Information Confirmation"),
        "save_info_confirmation_message": MessageLookupByLibrary.simpleMessage(
            "Do you want to save your information?"),
        "services": MessageLookupByLibrary.simpleMessage("Services"),
        "state": MessageLookupByLibrary.simpleMessage("State"),
        "status_dialogs":
            MessageLookupByLibrary.simpleMessage("Status Dialogs"),
        "stay": MessageLookupByLibrary.simpleMessage("Stay"),
        "submit_order_confirmation":
            MessageLookupByLibrary.simpleMessage("Submit Order Confirmation"),
        "submit_order_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                "Are you sure you want to submit this order?"),
        "success_dialog":
            MessageLookupByLibrary.simpleMessage("Success Dialog"),
        "success_message": MessageLookupByLibrary.simpleMessage(
            "Operation completed successfully!"),
        "today_orders": MessageLookupByLibrary.simpleMessage("Today Orders"),
        "total_earnings":
            MessageLookupByLibrary.simpleMessage("Total Earnings"),
        "warning_dialog":
            MessageLookupByLibrary.simpleMessage("Warning Dialog"),
        "warning_message": MessageLookupByLibrary.simpleMessage(
            "This action might have consequences."),
        "welcome": MessageLookupByLibrary.simpleMessage("Welcome"),
        "workshop_photo_url":
            MessageLookupByLibrary.simpleMessage("Workshop Photo"),
        "years_experience":
            MessageLookupByLibrary.simpleMessage("Years Experience"),
        "yes": MessageLookupByLibrary.simpleMessage("Yes")
      };
}
