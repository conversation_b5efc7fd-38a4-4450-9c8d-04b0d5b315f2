class AllCategories {
  AllCategories({
    required this.id,
    required this.name,
    // required this.slug,
    // required this.image,
    // required this.description,
    required this.parentId,
    // required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String name;
  // final String slug;
  // final dynamic image;
  // final String description;
  final int parentId;
  // final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory AllCategories.fromJson(Map<String, dynamic> json){
    return AllCategories(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      // slug: json["slug"] ?? "",
      // image: json["image"],
      // description: json["description"] ?? "",
      parentId: json["parent_id"] ?? 0,
      // status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    // "slug": slug,
    // "image": image,
    // "description": description,
    "parent_id": parentId,
    // "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

}
