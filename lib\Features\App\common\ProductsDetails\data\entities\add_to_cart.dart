class AddToCart {
  final int productId;
  final int quantity;
  final int? sizeId;
  final int? colorId;

  AddToCart({
    required this.productId,
    required this.quantity,
     this.sizeId,
     this.colorId,
  });

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'quantity': quantity,
      'size_id': sizeId,
      'color_id': colorId,
    };
  }

  factory AddToCart.fromMap(dynamic map) {
    return AddToCart(
      productId: map['product_id'],
      quantity: map['quantity'],
      sizeId: map['size_id'],
      colorId: map['color_id'],
    );
  }
}
