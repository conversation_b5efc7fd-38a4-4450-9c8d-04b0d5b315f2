import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../Config/Assets/image_svg.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../generated/l10n.dart';

class NoItemsCart extends StatelessWidget {
  const NoItemsCart({
    super.key,
    required this.local,
  });

  final S local;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(AppImagesSvg.cartEmpty),
          Text(
            local.massageInCart,
            style: AppTextStyles.bodyMediumBold,
          )
        ],
      ),
    );
  }
}
