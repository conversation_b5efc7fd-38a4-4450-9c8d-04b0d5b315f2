import 'dart:math';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MarkersModel {
  final String id;
  final String title;
  final int rating;
  final LatLng markerPosition;
  static late double _lat;
  static late double _lng;

  static set lat(double value) => _lat = value;
  static set lng( double value) => _lng = value;

  MarkersModel({
    required this.id,
    required this.title,
    required this.rating,
    required this.markerPosition,
  });

  static LatLng generateRandomPoint() {
    final random = Random();
    final double radius = 0.005;

    double randomLat = _lat + (random.nextDouble() - 0.5) * radius;
    double randomLng = _lng + (random.nextDouble() - 0.5) * radius;
    return LatLng(randomLat, randomLng);
  }

  static List<MarkersModel> markers = [
    MarkersModel(
      id: '1',
      title: 'Marker 1',
      rating: 5,
      markerPosition: generateRandomPoint(),
    ),
    MarkersModel(
      id: '2',
      title: 'Marker 2',
      rating: 4,
      markerPosition: generateRandomPoint(),
    ),
    MarkersModel(
      id: '3',
      title: 'Marker 3',
      rating: 4,
      markerPosition: generateRandomPoint(),
    ),
    MarkersModel(
      id: '4',
      title: 'Marker 4',
      rating: 4,
      markerPosition: generateRandomPoint(),
    ),
  ];

}