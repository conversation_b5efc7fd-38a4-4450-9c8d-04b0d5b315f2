class PostCarpenterAvailableModel {
  final String? appointmentDate;
  final String? appointmentTimeSlot;
  final int? carpentersRequired;

  PostCarpenterAvailableModel(
    this.appointmentDate,
    this.appointmentTimeSlot,
    this.carpentersRequired,
  );

  factory PostCarpenterAvailableModel.fromMap(Map<String, dynamic> map) {
    return PostCarpenterAvailableModel(
      map['appointment_date'],
      map['appointment_time_slot'],
      map['carpenters_required'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'appointment_date': appointmentDate,
      'appointment_time_slot': appointmentTimeSlot,
      'carpenters_required': carpentersRequired,
    };
  }
}
