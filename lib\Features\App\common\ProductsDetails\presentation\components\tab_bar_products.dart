import 'package:flutter/material.dart';
import 'package:buttons_tabbar/buttons_tabbar.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import 'product_information.dart';
import 'product_reviews_widget.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
class TabBarProducts extends StatelessWidget {
  const TabBarProducts({super.key});

  @override
  Widget build(BuildContext context) {
    final local = context.local;
    return SliverFillRemaining(
      fillOverscroll: false,
      hasScrollBody: true,
      child: DefaultTabController(
        initialIndex: 0,
        length: 2,
        child: Column(
          children: [
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
              child: ButtonsTabBar(
                borderWidth: 1,
                borderColor: AppColors.primaryColor,
                backgroundColor: AppColors.backgroundColor,
                unselectedBackgroundColor: AppColors.backgroundColor,
                labelStyle: AppTextStyles.bodyMediumSemiBold
                    .copyWith(color: AppColors.textColorBlack),
                unselectedLabelStyle: AppTextStyles.bodyMediumSemiBold
                    .copyWith(color: AppColors.grayscale50),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                unselectedBorderColor: AppColors.backgroundColor,
                tabs: [
                  Tab(text: local.Details),
                  Tab(text: local.Reviews),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                clipBehavior: Clip.none,

                physics: NeverScrollableScrollPhysics(),
                children: [
                  ProductInformation(),
                  ProductReviewsWidget(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
