import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../Config/Assets/image_svg.dart';
import '../../../../Core/Utils/Extensions/localizations_extension.dart';

import '../../../../Core/Resources/app_colors.dart';
import '../../../../Core/Resources/app_fonts.dart';

class LogoAndText extends StatelessWidget {
  const LogoAndText({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SvgPicture.asset(
          AppImagesSvg.logoDarkMode,
          height: 80.h,
          width: 80.w,
        ),
        Padding(
          padding: const EdgeInsets.all(15.0),
          child: Text(
            context.local.TitleLogoSplash,
            style: AppTextStyles.bodyXtraLargeMedium.copyWith(
              color: AppColors.textColorWhite,
              height: 1.57,
              letterSpacing: 0.07,
            ),
          ),
        ),
      ],
    );
  }
}
