import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Config/Routes/route_name.dart';
import '../../../../../main.dart';
import '../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Utils/Extensions/widget_extension.dart';
import '../../../../Global/Authentication/presentation/components/build_social_login_button.dart';

class SpaceOptimizerScreen extends StatelessWidget {
  const SpaceOptimizerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final local = context.local;
    return [
      SliverFillRemaining(
        fillOverscroll: true,
        hasScrollBody: false,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 15.h,
          children: [
            // BuildSocialLoginButton(
            //     text: local.RoomMeasurementFeature,
            //     isSpace: false,
            //     backgroundColor: AppColors.primaryColor,
            //     onPressed: ()=>kNavigationService.navigateTo(AppRoutes.areaMeasurementScreen)),
            CustomBuildButtonApp(
                text: local.FurnitureMeasurementFeature,
                isSpace: false,
                backgroundColor: AppColors.primaryColor,
                onPressed: () => kNavigationService
                    .navigateTo(AppRoutes.areaMeasurementScreen)),
            // BuildSocialLoginButton(
            //     text: local.EmptySpaceCalculatorFeature,
            //     isSpace: false,
            //     backgroundColor: AppColors.primaryColor,
            //     onPressed: () {}),
            CustomBuildButtonApp(
                text: local.FurnitureRecommendationFeature,
                isSpace: false,
                backgroundColor: AppColors.primaryColor,
                onPressed: () =>
                    kNavigationService.navigateTo(AppRoutes.recommendation)),
          ],
        ),
      )
    ].styledAppPages(withAll: true);
  }
}
