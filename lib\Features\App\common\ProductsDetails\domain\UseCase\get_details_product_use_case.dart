import 'package:dartz/dartz.dart';
import '../../data/entities/product_details_model.dart';
import '../repository/product_details_repo_impl.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

class GetDetailsProductUseCase {
  final ProductDetailsRepoImpl productDetailsRepoImpl;

  GetDetailsProductUseCase({required this.productDetailsRepoImpl});

  Future<Either<Failure, ViewProductDetails>> call({required int id}) {
    return productDetailsRepoImpl.getProductDetails(id: id);
  }
}
