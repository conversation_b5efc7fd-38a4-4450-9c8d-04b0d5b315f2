import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../ProductsDetails/data/entities/product_details_model.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';


import '../../data/data/home_data.dart';
import '../../data/repositories/home_repo.dart';

class HomeRepoImpl extends HomeRepo {
  @override
  Future<Either<Failure, List<ReviewProductsModel>>>
      getProductIsFeatured() async {
    try {
      final response = await HomeData.getDataProductIsFeatured();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, List<ReviewProductsModel>>>
      getProductIsPopular() async {
    try {
      final response = await HomeData.getDataProductIsPopular();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, ViewProductDetails>> getProductDetails(int id) async {
    try {
      final response = await HomeData.getProductsByID(id);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }


}
