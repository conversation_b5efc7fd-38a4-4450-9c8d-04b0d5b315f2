import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../Resources/app_colors.dart';
import '../../../Resources/app_fonts.dart';

class BuildChoiceChip extends StatelessWidget {
  const BuildChoiceChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ChoiceChip(
      showCheckmark: false,
      label: Text(label),
      selected: isSelected,
      selectedColor: AppColors.primaryColor,
      backgroundColor: AppColors.backgroundColor,
      labelStyle: AppTextStyles.bodySmallSemiBold.copyWith(
        color: isSelected ? AppColors.textColorWhite : AppColors.textColorBlack,
      ),
      onSelected: (_) => onTap(),
      labelPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 12).r,
      shape: RoundedRectangleBorder(
        side: BorderSide(color: AppColors.grayscale30),
        borderRadius: BorderRadius.circular(24).r,
      ),
    );
  }
}
