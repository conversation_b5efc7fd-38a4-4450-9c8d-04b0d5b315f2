import 'package:dio/dio.dart';
import 'api_password.dart';
import '../Local/local_storage_keys.dart';
import '../Local/local_storage_service.dart';
import 'api_endpoints.dart';

class DioHelper {
  static late Dio dio;

  static void init() {
    dio = Dio(
      BaseOptions(
        baseUrl: ApiEndpoints.baseUrl,
        receiveDataWhenStatusError: true,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        queryParameters: {
          'api_password': ApiPassword.password,
        },
      ),
    );

    // Add logging interceptor in debug mode
    // dio.interceptors.add(
    //   LogInterceptor(
    //     request: true,
    //     requestHeader: true,
    //     requestBody: true,
    //     responseHeader: true,
    //     responseBody: true,
    //     error: true,
    //   ),
    // );
  }

  /// Handles GET requests
  static Future<Response> getData({
    required String path,
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.get(
      path,
      queryParameters: queryParameters,
      options: await _getAuthorizationHeader(),
    );
  }

  /// Handles POST requests
  static Future<Response> postData({
    required String path,
    dynamic  data,
    Options ? options,
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.post(
      path,
      data: data,
      queryParameters: queryParameters,
      options:options?? await _getAuthorizationHeader(),
    );
  }

  /// Handles PUT requests
  static Future<Response> putData({
    required String path,
    required dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.put(
      path,
      data: data,
      queryParameters: queryParameters,
      options: await _getAuthorizationHeader(),
    );
  }

  /// Handles DELETE requests
  static Future<Response> deleteData({
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    return await dio.delete(
      path,
      data: data,
      queryParameters: queryParameters,
      options: await _getAuthorizationHeader(),
    );
  }

  static Future<Options> _getAuthorizationHeader() async {
    String? token = LocalStorageService.getValue(LocalStorageKeys.token);
    return Options(headers: {
      'Authorization': token != null && token.isNotEmpty ? 'Bearer $token' : '',
    });
  }
}
