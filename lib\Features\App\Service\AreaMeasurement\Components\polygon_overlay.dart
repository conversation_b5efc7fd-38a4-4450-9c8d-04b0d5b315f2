// import 'package:flutter/material.dart';
// import 'package:vector_math/vector_math_64.dart' as math;
//
// class PolygonOverlay extends StatelessWidget {
//   final List<math.Vector3> points;
//
//   const PolygonOverlay({
//     super.key,
//     required this.points,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return CustomPaint(
//       painter: PolygonPainter(points: points),
//       child: Container(),
//     );
//   }
// }
//
// class PolygonPainter extends CustomPainter {
//   final List<math.Vector3> points;
//
//   PolygonPainter({required this.points});
//
//   @override
//   void paint(Canvas canvas, Size size) {
//     if (points.length < 3) return;
//
//     final paint = Paint()
//       ..color = Colors.blue.withOpacity(0.3)
//       ..strokeWidth = 2
//       ..style = PaintingStyle.fill;
//
//     // This would need to be calculated based on AR camera view transformation
//     // For a real implementation, you'd need to map 3D world coordinates to 2D screen coordinates
//     // This is a simplified placeholder
//     final path = Path();
//
//     // In an actual implementation, you would need to:
//     // 1. Get the ARView's viewport dimensions
//     // 2. Get the AR camera's projection matrix
//     // 3. Project each 3D point to 2D screen coordinates using the matrix
//
//     // For now, this is a placeholder showing how you'd draw if you had the 2D points
//     // path.moveTo(projectedPoints[0].x, projectedPoints[0].y);
//     // for (int i = 1; i < projectedPoints.length; i++) {
//     //   path.lineTo(projectedPoints[i].x, projectedPoints[i].y);
//     // }
//     // path.close();
//
//     // canvas.drawPath(path, paint);
//
//     // For a proper implementation, you'd need to use ARKit/ARCore's APIs to get
//     // the projected points or render a custom overlay directly in the AR view
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
// }