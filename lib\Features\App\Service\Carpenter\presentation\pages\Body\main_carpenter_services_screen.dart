import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../../Core/Utils/Widget/Massages/custom_scaffold_messenger.dart';
import '../../../../../../../Core/Utils/Extensions/widget_extension.dart';
import '../../Cubit/CarpenterService/carpenter_service_cubit.dart';
import '../../components/build_button_carpenter_service.dart';
import '../../components/Main/build_list_services_carpenters.dart';
import '../../components/Main/offers_carpenter_service.dart';
import '../../components/Main/person_counter_widget.dart';
import '../../components/Main/title_carpenter_service_screen.dart';
import 'calendar_screen.dart';

class MainCarpenterServicesScreen extends StatelessWidget {
  const MainCarpenterServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return [
      SliverToBoxAdapter(
        child: Column(
          spacing: 40.h,
          children: [
            TitleCarpenterServiceScreen(),
            OffersCarpenterService(),
            BuildListServicesCarpenters(),
            PersonCounterWidget(),
          ],
        ),
      ),
      BuildButtonCarpenterService(
        onPressed: () {
          final cubit = context.read<CarpenterServiceCubit>();
          // if (cubit.state.number<=0){
          //   showCustomSnackBar(context, "Please Enter Number of carpenters you need", SnackBarType.info);
          //
          // }
          if (cubit.state.selectedService == null ||
              cubit.state.selectedService == -1) {
            showCustomSnackBar(context, "Please Select the services you need ",
                SnackBarType.info);
          } else {
            cubit.changeScreen(CalendarScreen());
          }
        },
        text: context.local.Proceed,
      )
    ].styledAppPages(
      withPadding: true,
      withScroll: true,
    );
  }
}
