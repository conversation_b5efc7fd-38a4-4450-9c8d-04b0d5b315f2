import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../data/entities/product_details_model.dart';
import '../../data/entities/add_to_cart.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Sources/product_details_sources.dart';


import '../../data/repositories/product_details_repo.dart';

class ProductDetailsRepoImpl extends ProductDetailsRepo {
  @override
  Future<Either<Failure, ViewProductDetails>> getProductDetails(
      {required int id}) async {
    try {
      final response = await ProductDetailsSources.getProductDetails(
        id: id,
      );
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, bool>> addToCart({required AddToCart item}) async {
    try {
      final response = await ProductDetailsSources.addToCart(item: item);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, Review>> addReview({required int rating,  String? comment, required int productId}) async{
    try {
      final response = await ProductDetailsSources.addReview(rating: rating, comment: comment, productId: productId);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {

      return Left(ServerFailure("Unknown error occurred"));
    }
  }
}
