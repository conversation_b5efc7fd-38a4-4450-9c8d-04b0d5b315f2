class ReviewProductsModel {
  ReviewProductsModel({
    required this.id,
    required this.name,
    // required this.slug,
    // required this.categoryId,
    required this.description,
    required this.additionalInfo,
    required this.price,
    required this.discountPrice,
    required this.dimensions,
    // required this.featured,
    // required this.popular,
    // required this.status,
    // required this.quantity,
    // required this.createdAt,
    // required this.updatedAt,
    required this.primaryImage,
    // required this.category,
  });

  final int id;
  final String name;
  // final String slug;
  // final int categoryId;
  final String description;
  final String additionalInfo;
  final String price;
  final String discountPrice;
  final String dimensions;
  // final int featured;
  // final int popular;
  // final int status;
  // final int quantity;
  // final DateTime? createdAt;
  // final DateTime? updatedAt;
  final PrimaryImage? primaryImage;
  // final Category? category;

  factory ReviewProductsModel.fromJson(Map<String, dynamic> json){
    return ReviewProductsModel(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      // slug: json["slug"] ?? "",
      // categoryId: json["category_id"] ?? 0,
      description: json["description"] ?? "",
      additionalInfo: json["additional_info"] ?? "",
      price: json["price"] ?? "",
      discountPrice: json["discount_price"] ?? "",
      dimensions: json["dimensions"] ?? "",
      // featured: json["featured"] ?? 0,
      // popular: json["popular"] ?? 0,
      // status: json["status"] ?? 0,
      // quantity: json["quantity"] ?? 0,
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      primaryImage: json["primary_image"] == null ? null : PrimaryImage.fromJson(json["primary_image"]),
      // category: json["category"] == null ? null : Category.fromJson(json["category"]),
    );
  }

  Map<String, dynamic> toMap() => {
    "id": id,
    "name": name,
    // "slug": slug,
    // "category_id": categoryId,
    "description": description,
    "additional_info": additionalInfo,
    "price": price,
    "discount_price": discountPrice,
    "dimensions": dimensions,
    // "featured": featured,
    // "popular": popular,
    // "status": status,
    // "quantity": quantity,
    // "created_at": createdAt?.toIso8601String(),
    // "updated_at": updatedAt?.toIso8601String(),
    "primary_image": primaryImage?.toJson(),
    // "category": category?.toJson(),
  };

}

// class Category {
//   Category({
//     required this.id,
//     required this.name,
//     required this.slug,
//     required this.image,
//     required this.description,
//     required this.parentId,
//     required this.status,
//     required this.createdAt,
//     required this.updatedAt,
//   });
//
//   final int id;
//   final String name;
//   final String slug;
//   final String image;
//   final String description;
//   final int parentId;
//   final int status;
//   final DateTime? createdAt;
//   final DateTime? updatedAt;
//
//   factory Category.fromJson(Map<String, dynamic> json){
//     return Category(
//       id: json["id"] ?? 0,
//       name: json["name"] ?? "",
//       slug: json["slug"] ?? "",
//       image: json["image"] ?? "",
//       description: json["description"] ?? "",
//       parentId: json["parent_id"] ?? 0,
//       status: json["status"] ?? 0,
//       createdAt: DateTime.tryParse(json["created_at"] ?? ""),
//       updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
//     );
//   }
//
//   Map<String, dynamic> toJson() => {
//     "id": id,
//     "name": name,
//     "slug": slug,
//     "image": image,
//     "description": description,
//     "parent_id": parentId,
//     "status": status,
//     "created_at": createdAt?.toIso8601String(),
//     "updated_at": updatedAt?.toIso8601String(),
//   };
//
// }

class PrimaryImage {
  PrimaryImage({
    // required this.id,
    // required this.productId,
    required this.imageUrl,
    // required this.isPrimary,
    // required this.createdAt,
    // required this.updatedAt,
  });

  // final int id;
  // final int productId;
  final String imageUrl;
  // final int isPrimary;
  // final DateTime? createdAt;
  // final DateTime? updatedAt;

  factory PrimaryImage.fromJson(Map<String, dynamic> json){
    return PrimaryImage(
      // id: json["id"] ?? 0,
      // productId: json["product_id"] ?? 0,
      imageUrl: json["image_url"] ?? "",
      // isPrimary: json["is_primary"] ?? 0,
      // createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      // updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    // "id": id,
    // "product_id": productId,
    "image_url": imageUrl,
    // "is_primary": isPrimary,
    // "created_at": createdAt?.toIso8601String(),
    // "updated_at": updatedAt?.toIso8601String(),
  };

}
