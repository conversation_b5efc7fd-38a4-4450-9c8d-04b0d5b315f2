import 'package:dartz/dartz.dart';
import '../../data/Model/all_categories.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repositories/explore_repo_impl.dart';


class AllCategoriesUseCase {
  final ExploreRepoImpl exploreRepoImpl;

  AllCategoriesUseCase({required this.exploreRepoImpl});

  Future<Either<Failure, List<AllCategories>>> call() async =>
      await exploreRepoImpl.getAllCategories();
}
