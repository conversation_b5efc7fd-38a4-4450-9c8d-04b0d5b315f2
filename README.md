# Zan - Furniture & Carpentry Mobile Application

## 📱 Project Overview

**Zan** is a comprehensive Flutter mobile application designed for furniture shopping and carpentry services. The app provides a complete ecosystem for customers to browse furniture, place orders, and connect with professional carpenters, while offering workshop owners a powerful dashboard to manage their business operations.

## 🏗️ Architecture Overview

The application follows **Clean Architecture** principles with a well-structured, scalable design:

```
┌─────────────────────────────────────────────────────────────────┐
│                    User Interface (Presentation Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Cubits/BLoCs (State Management)                            │
│  • Pages/Screens (UI Components)                              │
│  • Widgets (Reusable UI Elements)                             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Application Business Logic (Domain Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Use Cases (Business Logic)                                 │
│  • Repository Interfaces (Contracts)                          │
│  • Entities (Business Models)                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Data Management (Data Layer)                       │
├─────────────────────────────────────────────────────────────────┤
│  • Repository Implementations                                 │
│  • Data Sources (Remote API, Local Storage)                   │
│  • Data Models                                                │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Core Infrastructure                          │
├─────────────────────────────────────────────────────────────────┤
│  • Remote Storage (API Services)                              │
│  • Local Storage (Hive Database)                              │
│  • Service Locator (Dependency Injection)                     │
└─────────────────────────────────────────────────────────────────┘
```

## ✨ Key Features

### 🛍️ Customer Features
- **E-commerce**: Browse new and used furniture with advanced filtering
- **AI Product Recommendations**: Smart suggestions based on room analysis
- **Shopping Cart**: Add, remove, and manage items with real-time updates
- **Order Management**: Track order status and complete purchase history
- **Equipment & Truck Rentals**: Rent tools and transportation services
- **User Profile**: Manage personal information, addresses, and preferences
- **Favorites**: Save preferred products for later purchase
- **Smart Chatbot**: Real-time customer support and instant help
- **Real-time Booking**: Schedule services and appointments
- **Payment Integration**: Secure payments via Stripe

### 🔧 Service Features
- **AR Room Analysis**: 3D visualization and room space mapping
- **AI Furniture Detection**: YOLOv8-powered object detection and room analysis
- **Damage Analysis**: AI-powered furniture condition assessment
- **3D Space Optimizer**: Visualize furniture placement in real space
- **Carpenter Services**: Connect with AI-verified professional carpenters
- **Equipment Rental System**: Tool availability tracking and rental durations

### 👨‍🔧 Carpenter Features
- **AI Verification System**: Automated carpenter verification and rating
- **Service Listing**: Manage available services and specializations
- **Calendar Orders**: Schedule and manage appointments
- **Profile Dashboard**: Comprehensive carpenter profile management
- **Financial Tracking**: Revenue tracking and payment management
- **Rental Marketplace**: List and rent tools to other professionals

### 🏢 Workshop Dashboard
- **Order Management**: View and process incoming orders
- **Calendar Integration**: Schedule and track appointments
- **Analytics Dashboard**: KPIs, sales reports, and performance metrics
- **Profile Management**: Carpenter profile and service details
- **Financial Overview**: Revenue tracking and payment history

## 🛠️ Technical Stack

### Mobile Development Stack

| Technology | Usage | Version |
|------------|-------|---------|
| **Flutter** | Cross-platform mobile development | 3.x |
| **Dart** | Programming language | 3.x |
| **BLoC/Cubit** | State management pattern | ^9.1.0 |
| **GetIt** | Dependency injection | ^8.0.3 |
| **Hive** | Local database storage | ^2.2.3 |
| **Dio** | HTTP client for API calls | ^5.8.0 |
| **Stripe** | Payment processing | ^11.1.0 |
| **ARCore Plugin** | Augmented reality features | ^0.0.9 |
| **Model Viewer** | 3D model visualization | ^1.8.0 |
| **YOLOv8** | AI object detection | Python integration |

### Architecture & Design
- **Clean Architecture**: Three-tier design with clear separation of concerns
- **MVVM Pattern**: Model-View-ViewModel for UI layer
- **Repository Pattern**: Data access abstraction
- **Use Cases**: Business logic encapsulation
- **Material Design 3**: Modern UI/UX guidelines
- **Responsive Design**: Adaptive layouts for all screen sizes

### Key Dependencies
```yaml
# State Management
flutter_bloc: ^9.1.0
bloc: ^9.0.0
get_it: ^8.0.3

# UI & Styling
flutter_screenutil: ^5.9.3
google_fonts: ^6.2.1
flutter_svg: ^2.0.17
cached_network_image: ^3.4.1

# Navigation
convex_bottom_bar: ^3.2.0

# Data & Storage
hive: ^2.2.3
hive_flutter: ^1.1.0
dio: ^5.8.0+1

# Payment
flutter_stripe: ^11.1.0

# AR/3D Features
arcore_flutter_plugin: ^0.0.9
model_viewer_plus: ^1.8.0

# AI Integration
camera: ^0.10.5
image_picker: ^1.1.2

# Utilities
dartz: ^0.10.1
permission_handler: ^11.3.1
```

## 📁 Project Structure

The project follows Clean Architecture principles with a clear separation of concerns:

```
lib/
├── Config/                     # App configuration and setup
│   ├── Routes/                 # Navigation and routing
│   ├── Themes/                 # Light and dark themes
│   ├── Cubit/                  # Global state management
│   └── app_config.dart         # App constants and settings
├── Core/                       # Core infrastructure
│   ├── Models/                 # Shared data models
│   ├── Resources/              # Colors, constants, assets
│   ├── Services/               # External services
│   │   ├── Payment/Strip/      # Stripe payment integration
│   │   └── ServiceLocator/     # Dependency injection
│   ├── Storage/                # Data persistence
│   │   ├── Local/              # Hive local storage
│   │   └── Remote/             # API services
│   └── Utils/                  # Utility functions and widgets
├── Features/                   # Feature modules
│   ├── Global/                 # Global features
│   │   ├── Authentication/     # Login/Register
│   │   ├── Boarding/           # Onboarding screens
│   │   └── Splash/             # Splash screen
│   ├── App/                    # Main app features
│   │   ├── common/             # Shared app features
│   │   │   ├── Home/           # Home screen
│   │   │   ├── Explore/        # Product categories
│   │   │   ├── Search/         # Search functionality
│   │   │   ├── Cart/           # Shopping cart
│   │   │   ├── Orders/         # Order management
│   │   │   ├── Favorite/       # Favorites
│   │   │   ├── Main/           # Main navigation
│   │   │   ├── ProductsDetails/# Product details
│   │   │   └── SpaceOptimizer/ # 3D visualization
│   │   ├── Service/            # Service features
│   │   │   ├── AreaMeasurement/# AR measurement
│   │   │   ├── Recommendation/ # AI recommendations
│   │   │   └── Carpenter/      # Carpenter services
│   │   ├── User/               # User profile
│   │   └── WorkshopDashboard/  # Carpenter dashboard
│   └── Admin/                  # Admin features
└── generated/                  # Generated files (localization)
```

## � Mobile App Screens

The mobile application consists of several key screen categories designed for optimal user experience:

### 🚀 Core Navigation Screens
- **Splash Screen**: App initialization and loading
- **Onboarding**: Interactive app introduction and feature walkthrough
- **Authentication**: Login and registration with social media integration

### 🏠 Main Application Screens
- **Home**: Main dashboard with featured products and quick access
- **Explore**: Category browsing with advanced filtering options
- **Search**: Smart search with real-time results and suggestions
- **Scan**: Camera-based product scanning and AR features
- **Profile**: User account management and settings

### 🛠️ Service Tab Screens
- **Chatbot**: AI-powered customer support and assistance
- **Carpenter**: Professional carpenter services and booking
- **Tools**: Equipment rental marketplace and availability
- **Trucks**: Transportation services and logistics booking

### 📋 Management Screens
- **Orders**: Order tracking, history, and management
- **Calendar**: Appointment scheduling and service bookings
- **Cart**: Shopping cart with item management
- **Favorites**: Saved products and wishlist
- **Payment**: Secure payment processing and methods

## 🤖 AI Modules Integration

The mobile app integrates several AI-powered modules for enhanced functionality:

### 🎯 YOLOv8 Object Detection
- **Furniture Detection**: Real-time identification of furniture items
- **Room Space Mapping**: Automatic room layout analysis
- **Damage Assessment**: AI-powered condition evaluation
- **Product Recognition**: Camera-based product identification

### 🧠 AI Recommendation Engine
- **Smart Suggestions**: Personalized furniture recommendations
- **Room-based Analysis**: Suggestions based on detected room features
- **User Behavior Learning**: Adaptive recommendations based on preferences
- **Style Matching**: Furniture style compatibility analysis

### 💬 Intelligent Chatbot
- **Natural Language Processing**: Understanding user queries
- **Context-aware Responses**: Relevant and helpful assistance
- **Multi-language Support**: Arabic and English communication
- **Service Integration**: Direct booking and order assistance

### 🔍 Damage Analysis System
- **Computer Vision**: Automated furniture condition assessment
- **Repair Recommendations**: Suggested maintenance and repairs
- **Cost Estimation**: AI-powered repair cost calculations
- **Before/After Comparison**: Visual damage documentation

## �🔧 Core Features Deep Dive

### 🏠 Home Screen
The home screen serves as the main entry point featuring:
- **Featured Products**: AI-curated highlighted furniture items
- **Popular Products**: Trending items based on user interactions and AI analysis
- **Category Navigation**: Quick access to product categories
- **Search Integration**: Instant search functionality with AI suggestions
- **User Profile Access**: Quick access to user settings and preferences

### 🔍 Explore & Search
Advanced product discovery system:
- **Category Filtering**: Browse by furniture categories
- **Real-time Search**: Instant search results as you type
- **Product Grid**: Responsive grid layout for product display
- **Caching System**: Optimized performance with cached data

### 🛒 Shopping Cart & Orders
Complete e-commerce functionality:
- **Cart Management**: Add, remove, and update quantities
- **Order Tracking**: Real-time order status updates
- **Order History**: Complete purchase history
- **Coupon System**: Apply and manage discount coupons

### 💳 Payment System
Secure payment processing with Stripe:
- **Payment Intent Creation**: Secure payment initialization
- **Ephemeral Keys**: Temporary authentication for payments
- **Payment Sheet**: Native payment UI
- **Transaction Security**: PCI-compliant payment processing

### 📐 AR Area Measurement
Advanced AR-powered measurement tool:
- **Point-based Measurement**: Tap to place measurement points
- **Real-time Calculation**: Instant area calculations
- **Multiple Units**: Support for cm² and ft² measurements
- **3D Visualization**: Visual representation of measured areas
- **Haptic Feedback**: Enhanced user interaction

### 🤖 AI Furniture Recommendation
Intelligent recommendation system:
- **Photo Analysis**: Upload room photos for analysis
- **Room Type Selection**: Choose from predefined room types
- **Dimension Input**: Specify room dimensions
- **Smart Suggestions**: AI-powered furniture recommendations
- **Visual Results**: Display recommended products with images

### 🏢 Workshop Dashboard
Comprehensive carpenter management system:
- **Order Calendar**: Schedule and manage appointments
- **Analytics Dashboard**: Sales performance and metrics
- **Profile Management**: Carpenter profile and services
- **Order Processing**: Handle incoming orders efficiently
- **Monthly Sales Charts**: Visual sales data representation

## 🔄 State Management Architecture

The app uses **BLoC/Cubit** pattern for state management with the following structure:

### Global State Management
- **SettingsCubit**: App-wide settings (theme, language, internet connectivity)
- **MainCubit**: Bottom navigation and main app flow
- **UserCubit**: User authentication and profile management

### Feature-Specific State Management
- **HomeCubit**: Home screen data and featured products
- **ExploreCubit**: Category browsing and product filtering
- **SearchCubit**: Search functionality and results
- **CartCubit**: Shopping cart operations
- **OrdersCubit**: Order management and tracking
- **AuthenticationCubit**: Login and registration flows
- **AreaMeasurementCubit**: AR measurement functionality
- **RecommendationCubit**: AI recommendation system
- **DashboardCubit**: Workshop dashboard navigation

### Service Locator Pattern
The app uses **GetIt** for dependency injection:

```dart
// Service registration
sl.registerLazySingleton<AuthenticationRepoImpl>(() => AuthenticationRepoImpl());
sl.registerFactory<HomeCubit>(() => HomeCubit(
  sl<GetFeaturedUseCase>(),
  sl<GetPopularUseCase>(),
  sl<ProductDetailsUseCase>(),
));
```

## 🌐 API Integration

### Base API Configuration
- **Base URL**: Configurable API endpoint
- **Authentication**: Bearer token-based authentication
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Caching**: Local caching for improved performance


### Data Models
The app uses strongly-typed data models with JSON serialization:
- **UserDataModel**: User profile and authentication data
- **ReviewProductsModel**: Product information and details
- **AllCategories**: Product category data
- **PaymentModel**: Stripe payment integration models

## 🎨 UI/UX Design System

### Design Principles
- **Material Design**: Following Material Design 3 guidelines
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Internationalization**: Arabic and English language support

### Color Scheme
- **Primary Colors**: Custom brand colors
- **Grayscale**: Comprehensive grayscale palette
- **Semantic Colors**: Success, error, warning, and info colors

### Typography
- **Google Fonts**: Custom font integration
- **Responsive Text**: Screen-size adaptive typography
- **Accessibility**: High contrast and readable text

### Components
- **Custom Widgets**: Reusable UI components
- **Animation Support**: Smooth transitions and micro-interactions
- **Loading States**: Skeleton loading and progress indicators

## 🚀 Getting Started

### Prerequisites
- **Flutter SDK**: Version 3.x or higher
- **Dart SDK**: Version 3.x or higher
- **Android Studio** or **VS Code** with Flutter extensions
- **Xcode** (for iOS development)
- **Android SDK**: API level 21 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/zan.git
   cd zan
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API Keys**

   Create a `.env` file in the root directory and add your API keys:
   ```env
   STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   API_BASE_URL=your_api_base_url
   ```

4. **Run the app**
   ```bash
   # For development
   flutter run

   # For release
   flutter run --release
   ```

### Platform-Specific Setup

#### Android
1. **Minimum SDK**: API level 21 (Android 5.0)
2. **Target SDK**: API level 34
3. **Permissions**: Camera, Internet, Storage
4. **ARCore**: Ensure ARCore is supported on target devices

#### iOS
1. **Minimum iOS**: 12.0
2. **Permissions**: Camera, Photo Library
3. **ARKit**: Ensure ARKit is supported on target devices

## 📱 Build & Deployment

### Android Build
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release
```

## 🔧 Development Guidelines

### Code Style
- Follow **Dart Style Guide**
- Use **flutter_lints** for code analysis
- Implement **Clean Architecture** principles
- Write comprehensive **documentation**

### Git Workflow
1. Create feature branches from `develop`
2. Use conventional commit messages
3. Submit pull requests for code review
4. Ensure all tests pass before merging

### State Management Best Practices
- Use **Cubit** for simple state management
- Use **BLoC** for complex business logic
- Implement proper error handling
- Use **Either** type for error handling with **dartz**

## 🌍 Internationalization

The app supports multiple languages:
- **English** (en)
- **Arabic** (ar)

### Adding New Languages
1. Add language code to `supportedLocales`
2. Create new `.arb` file in `lib/l10n/`
3. Run `flutter gen-l10n` to generate translations

## 🔐 Security Features

- **Token-based Authentication**: Secure JWT token management
- **Local Storage Encryption**: Sensitive data encryption with Hive
- **API Security**: HTTPS-only communication
- **Payment Security**: PCI-compliant Stripe integration
- **Permission Management**: Granular app permissions

## 📊 Performance Optimization

- **Image Caching**: Efficient image loading with `cached_network_image`
- **Lazy Loading**: On-demand data loading
- **State Persistence**: Maintain app state across sessions
- **Memory Management**: Proper disposal of resources
- **Network Optimization**: Request caching and retry mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🎓 Academic Information

This project was developed for academic purposes as part of a graduation project at:

**Future Academy - Higher Future Institute for Specialized Technological Studies**
- **School**: Computer Science Department
- **Academic Year**: 2024-2025
- **Project Type**: Graduation Project
- **Supervisor**: Dr. Ibrahim Abd-Allatif

### 🏆 Project Highlights
- **AI Integration**: Advanced machine learning models for furniture detection and recommendations
- **AR Technology**: Real-time augmented reality for space measurement and visualization
- **Cross-platform Development**: Single codebase for both Android and iOS
- **Modern Architecture**: Clean Architecture with MVVM pattern implementation
- **Industry-ready Solution**: Addresses real-world problems in carpentry and furniture industry

## 📄 License

This project was developed for academic purposes at **Future Academy - Higher Future Institute for Specialized Technological Studies**, School of Computer Science, May 2025.

## 👥 Development Team

- **Samuael Adel Fareed** - Backend Developer & Project Lead
- **Mahmoud Elsayed Khiralla** - Backend Developer 
- **Ziad Mohamed Elnagar** - Pen Tester 
- **Maha Abdelmoneam Mohamed** - Ai
- **Ahmed Mohamed Ahmed** - Frontend Developer 
- **Felopater Sameh Salama** - Mobile Appliapplication 
- **Supervisor**: Dr. Ibrahim Abd-Allatif - Academic Supervisor

## 📞 Documentation

- [Project Wiki]

---

**Built with ❤️ using Flutter**
