# Zan - Furniture & Carpentry Mobile Application

## 📱 Project Overview

**Zan** is a comprehensive Flutter mobile application designed for furniture shopping and carpentry services. The app provides a complete ecosystem for customers to browse furniture, place orders, and connect with professional carpenters, while offering workshop owners a powerful dashboard to manage their business operations.

## 🏗️ Architecture Overview

The application follows **Clean Architecture** principles with a well-structured, scalable design:

```
┌─────────────────────────────────────────────────────────────────┐
│                    User Interface (Presentation Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Cubits/BLoCs (State Management)                            │
│  • Pages/Screens (UI Components)                              │
│  • Widgets (Reusable UI Elements)                             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Application Business Logic (Domain Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Use Cases (Business Logic)                                 │
│  • Repository Interfaces (Contracts)                          │
│  • Entities (Business Models)                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Data Management (Data Layer)                       │
├─────────────────────────────────────────────────────────────────┤
│  • Repository Implementations                                 │
│  • Data Sources (Remote API, Local Storage)                   │
│  • Data Models                                                │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Core Infrastructure                          │
├─────────────────────────────────────────────────────────────────┤
│  • Remote Storage (API Services)                              │
│  • Local Storage (Hive Database)                              │
│  • Service Locator (Dependency Injection)                     │
└─────────────────────────────────────────────────────────────────┘
```

## ✨ Key Features

### 🛍️ Customer Features
- **Product Browsing**: Explore furniture categories with advanced filtering
- **Search Functionality**: Smart search with real-time results
- **Shopping Cart**: Add, remove, and manage items
- **Order Management**: Track order status and history
- **User Profile**: Manage personal information and addresses
- **Favorites**: Save preferred products for later
- **Payment Integration**: Secure payments via Stripe

### 🔧 Service Features
- **Area Measurement**: AR-powered room measurement tool
- **Furniture Recommendation**: AI-driven suggestions based on room photos
- **3D Space Optimizer**: Visualize furniture in 3D space
- **Carpenter Services**: Connect with professional carpenters

### 👨‍🔧 Workshop Dashboard
- **Order Management**: View and manage incoming orders
- **Calendar Integration**: Schedule and track appointments
- **Analytics Dashboard**: Sales reports and performance metrics
- **Profile Management**: Carpenter profile and service details

## 🛠️ Technical Stack

### Core Technologies
- **Framework**: Flutter 3.x
- **Language**: Dart
- **State Management**: BLoC/Cubit Pattern
- **Dependency Injection**: GetIt Service Locator
- **Architecture**: Clean Architecture with MVVM

### Key Dependencies
```yaml
# State Management
flutter_bloc: ^9.1.0
bloc: ^9.0.0
get_it: ^8.0.3

# UI & Styling
flutter_screenutil: ^5.9.3
google_fonts: ^6.2.1
flutter_svg: ^2.0.17
cached_network_image: ^3.4.1

# Navigation
convex_bottom_bar: ^3.2.0

# Data & Storage
hive: ^2.2.3
hive_flutter: ^1.1.0
dio: ^5.8.0+1

# Payment
flutter_stripe: ^11.1.0

# AR/3D Features
arcore_flutter_plugin: ^0.0.9
model_viewer_plus: ^1.8.0

# Utilities
dartz: ^0.10.1
permission_handler: ^11.3.1
image_picker: ^1.1.2
```

## 📁 Project Structure

The project follows Clean Architecture principles with a clear separation of concerns:

```
lib/
├── Config/                     # App configuration and setup
│   ├── Routes/                 # Navigation and routing
│   ├── Themes/                 # Light and dark themes
│   ├── Cubit/                  # Global state management
│   └── app_config.dart         # App constants and settings
├── Core/                       # Core infrastructure
│   ├── Models/                 # Shared data models
│   ├── Resources/              # Colors, constants, assets
│   ├── Services/               # External services
│   │   ├── Payment/Strip/      # Stripe payment integration
│   │   └── ServiceLocator/     # Dependency injection
│   ├── Storage/                # Data persistence
│   │   ├── Local/              # Hive local storage
│   │   └── Remote/             # API services
│   └── Utils/                  # Utility functions and widgets
├── Features/                   # Feature modules
│   ├── Global/                 # Global features
│   │   ├── Authentication/     # Login/Register
│   │   ├── Boarding/           # Onboarding screens
│   │   └── Splash/             # Splash screen
│   ├── App/                    # Main app features
│   │   ├── common/             # Shared app features
│   │   │   ├── Home/           # Home screen
│   │   │   ├── Explore/        # Product categories
│   │   │   ├── Search/         # Search functionality
│   │   │   ├── Cart/           # Shopping cart
│   │   │   ├── Orders/         # Order management
│   │   │   ├── Favorite/       # Favorites
│   │   │   ├── Main/           # Main navigation
│   │   │   ├── ProductsDetails/# Product details
│   │   │   └── SpaceOptimizer/ # 3D visualization
│   │   ├── Service/            # Service features
│   │   │   ├── AreaMeasurement/# AR measurement
│   │   │   ├── Recommendation/ # AI recommendations
│   │   │   └── Carpenter/      # Carpenter services
│   │   ├── User/               # User profile
│   │   └── WorkshopDashboard/  # Carpenter dashboard
│   └── Admin/                  # Admin features
└── generated/                  # Generated files (localization)
```

## 🔧 Core Features Deep Dive

### 🏠 Home Screen
The home screen serves as the main entry point featuring:
- **Featured Products**: Highlighted furniture items
- **Popular Products**: Trending items based on user interactions
- **Category Navigation**: Quick access to product categories
- **Search Integration**: Instant search functionality
- **User Profile Access**: Quick access to user settings

### 🔍 Explore & Search
Advanced product discovery system:
- **Category Filtering**: Browse by furniture categories
- **Real-time Search**: Instant search results as you type
- **Product Grid**: Responsive grid layout for product display
- **Caching System**: Optimized performance with cached data

### 🛒 Shopping Cart & Orders
Complete e-commerce functionality:
- **Cart Management**: Add, remove, and update quantities
- **Order Tracking**: Real-time order status updates
- **Order History**: Complete purchase history
- **Coupon System**: Apply and manage discount coupons

### 💳 Payment System
Secure payment processing with Stripe:
- **Payment Intent Creation**: Secure payment initialization
- **Ephemeral Keys**: Temporary authentication for payments
- **Payment Sheet**: Native payment UI
- **Transaction Security**: PCI-compliant payment processing

### 📐 AR Area Measurement
Advanced AR-powered measurement tool:
- **Point-based Measurement**: Tap to place measurement points
- **Real-time Calculation**: Instant area calculations
- **Multiple Units**: Support for cm² and ft² measurements
- **3D Visualization**: Visual representation of measured areas
- **Haptic Feedback**: Enhanced user interaction

### 🤖 AI Furniture Recommendation
Intelligent recommendation system:
- **Photo Analysis**: Upload room photos for analysis
- **Room Type Selection**: Choose from predefined room types
- **Dimension Input**: Specify room dimensions
- **Smart Suggestions**: AI-powered furniture recommendations
- **Visual Results**: Display recommended products with images

### 🏢 Workshop Dashboard
Comprehensive carpenter management system:
- **Order Calendar**: Schedule and manage appointments
- **Analytics Dashboard**: Sales performance and metrics
- **Profile Management**: Carpenter profile and services
- **Order Processing**: Handle incoming orders efficiently
- **Monthly Sales Charts**: Visual sales data representation

## 🔄 State Management Architecture

The app uses **BLoC/Cubit** pattern for state management with the following structure:

### Global State Management
- **SettingsCubit**: App-wide settings (theme, language, internet connectivity)
- **MainCubit**: Bottom navigation and main app flow
- **UserCubit**: User authentication and profile management

### Feature-Specific State Management
- **HomeCubit**: Home screen data and featured products
- **ExploreCubit**: Category browsing and product filtering
- **SearchCubit**: Search functionality and results
- **CartCubit**: Shopping cart operations
- **OrdersCubit**: Order management and tracking
- **AuthenticationCubit**: Login and registration flows
- **AreaMeasurementCubit**: AR measurement functionality
- **RecommendationCubit**: AI recommendation system
- **DashboardCubit**: Workshop dashboard navigation

### Service Locator Pattern
The app uses **GetIt** for dependency injection:

```dart
// Service registration
sl.registerLazySingleton<AuthenticationRepoImpl>(() => AuthenticationRepoImpl());
sl.registerFactory<HomeCubit>(() => HomeCubit(
  sl<GetFeaturedUseCase>(),
  sl<GetPopularUseCase>(),
  sl<ProductDetailsUseCase>(),
));
```

## 🌐 API Integration

### Base API Configuration
- **Base URL**: Configurable API endpoint
- **Authentication**: Bearer token-based authentication
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Caching**: Local caching for improved performance

### Key API Endpoints
```dart
// Authentication
static const String loginUser = "auth/login";
static const String registerUser = "auth/register";

// Products
static const String allCategory = "categories";
static const String getProductsByCategories = "categories/";
static const String product = "products";

// Orders
static const String orders = "orders";

// Carpenter Services
static const String allCarpentryServices = "carpentry-services";
static const String carpenters = "carpentry-orders/available-carpenters";
static const String createOrderCarpenters = "carpentry-orders";

// AI Chat
static const String chatAiSend = "/chat/send";
static const String chatAiGet = "/chat/history";
static const String chatAiFaqs = "/chat/faqs";
```

### Data Models
The app uses strongly-typed data models with JSON serialization:
- **UserDataModel**: User profile and authentication data
- **ReviewProductsModel**: Product information and details
- **AllCategories**: Product category data
- **PaymentModel**: Stripe payment integration models

## 🎨 UI/UX Design System

### Design Principles
- **Material Design**: Following Material Design 3 guidelines
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Dark/Light Theme**: Complete theme support
- **Internationalization**: Arabic and English language support

### Color Scheme
- **Primary Colors**: Custom brand colors
- **Grayscale**: Comprehensive grayscale palette
- **Semantic Colors**: Success, error, warning, and info colors

### Typography
- **Google Fonts**: Custom font integration
- **Responsive Text**: Screen-size adaptive typography
- **Accessibility**: High contrast and readable text

### Components
- **Custom Widgets**: Reusable UI components
- **Animation Support**: Smooth transitions and micro-interactions
- **Loading States**: Skeleton loading and progress indicators

## 🚀 Getting Started

### Prerequisites
- **Flutter SDK**: Version 3.x or higher
- **Dart SDK**: Version 3.x or higher
- **Android Studio** or **VS Code** with Flutter extensions
- **Xcode** (for iOS development)
- **Android SDK**: API level 21 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/zan.git
   cd zan
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API Keys**

   Create a `.env` file in the root directory and add your API keys:
   ```env
   STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   API_BASE_URL=your_api_base_url
   ```

4. **Run the app**
   ```bash
   # For development
   flutter run

   # For release
   flutter run --release
   ```

### Platform-Specific Setup

#### Android
1. **Minimum SDK**: API level 21 (Android 5.0)
2. **Target SDK**: API level 34
3. **Permissions**: Camera, Internet, Storage
4. **ARCore**: Ensure ARCore is supported on target devices

#### iOS
1. **Minimum iOS**: 12.0
2. **Permissions**: Camera, Photo Library
3. **ARKit**: Ensure ARKit is supported on target devices

## 🧪 Testing

### Running Tests
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### Test Structure
- **Unit Tests**: Business logic and use cases
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end user flows

## 📱 Build & Deployment

### Android Build
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release
```

## 🔧 Development Guidelines

### Code Style
- Follow **Dart Style Guide**
- Use **flutter_lints** for code analysis
- Implement **Clean Architecture** principles
- Write comprehensive **documentation**

### Git Workflow
1. Create feature branches from `develop`
2. Use conventional commit messages
3. Submit pull requests for code review
4. Ensure all tests pass before merging

### State Management Best Practices
- Use **Cubit** for simple state management
- Use **BLoC** for complex business logic
- Implement proper error handling
- Use **Either** type for error handling with **dartz**

## 🌍 Internationalization

The app supports multiple languages:
- **English** (en)
- **Arabic** (ar)

### Adding New Languages
1. Add language code to `supportedLocales`
2. Create new `.arb` file in `lib/l10n/`
3. Run `flutter gen-l10n` to generate translations

## 🔐 Security Features

- **Token-based Authentication**: Secure JWT token management
- **Local Storage Encryption**: Sensitive data encryption with Hive
- **API Security**: HTTPS-only communication
- **Payment Security**: PCI-compliant Stripe integration
- **Permission Management**: Granular app permissions

## 📊 Performance Optimization

- **Image Caching**: Efficient image loading with `cached_network_image`
- **Lazy Loading**: On-demand data loading
- **State Persistence**: Maintain app state across sessions
- **Memory Management**: Proper disposal of resources
- **Network Optimization**: Request caching and retry mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Project Lead**: [Your Name]
- **Flutter Developers**: [Developer Names]
- **UI/UX Designers**: [Designer Names]
- **Backend Developers**: [Backend Developer Names]

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [Project Wiki]
- **Issues**: [GitHub Issues]

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- **v1.1.0** - Added AR measurement functionality
- **v1.2.0** - Implemented AI recommendation system
- **v1.3.0** - Workshop dashboard enhancements

---

**Built with ❤️ using Flutter**
