# Zan - AI-Powered Furniture & Carpentry Mobile Application

## 🎓 Graduation Project 2025

**Zan** is a comprehensive Flutter mobile application developed as a graduation project for **Future Academy - Higher Future Institute for Specialized Technological Studies**. This AI-powered platform integrates furniture e-commerce, carpentry services, and advanced technologies including AR measurement tools and intelligent recommendation systems.

### 📚 Academic Project Details

- **Institution**: Future Academy - Higher Future Institute for Specialized Technological Studies
- **Department**: School of Computer Science
- **Academic Year**: 2024-2025
- **Project Type**: Final Year Graduation Project
- **Supervisor**: Dr. <PERSON>-Allatif
- **Submission Date**: May 2025
- **Project Duration**: 8 months (September 2024 - May 2025)
- **Team Size**: 6 students + 1 supervisor

### 🎯 Project Objectives
- **Primary Goal**: Develop a comprehensive mobile solution for the furniture and carpentry industry
- **Technical Goals**: Implement Clean Architecture, integrate AR/AI technologies, ensure cross-platform compatibility
- **Business Goals**: Address industry fragmentation and improve customer-carpenter connectivity
- **Academic Goals**: Apply software engineering principles and modern development practices

## 📱 Mobile Application Overview

**Zan** is a cross-platform mobile application built with Flutter that addresses the fragmentation in the carpentry and furniture industry by providing a unified platform for customers, carpenters, and service providers.

## 🏗️ Architecture Overview

The application follows **Clean Architecture** principles with a well-structured, scalable design:

```
┌─────────────────────────────────────────────────────────────────┐
│                    User Interface (Presentation Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Cubits/BLoCs (State Management)                            │
│  • Pages/Screens (UI Components)                              │
│  • Widgets (Reusable UI Elements)                             │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Application Business Logic (Domain Layer)          │
├─────────────────────────────────────────────────────────────────┤
│  • Use Cases (Business Logic)                                 │
│  • Repository Interfaces (Contracts)                          │
│  • Entities (Business Models)                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│              Data Management (Data Layer)                       │
├─────────────────────────────────────────────────────────────────┤
│  • Repository Implementations                                 │
│  • Data Sources (Remote API, Local Storage)                   │
│  • Data Models                                                │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Core Infrastructure                          │
├─────────────────────────────────────────────────────────────────┤
│  • Remote Storage (API Services)                              │
│  • Local Storage (Hive Database)                              │
│  • Service Locator (Dependency Injection)                     │
└─────────────────────────────────────────────────────────────────┘
```

## ✨ Key Features

### 🛍️ Customer Features (Mobile App)

- **Product Browsing**: Browse furniture categories with filtering
- **Search Functionality**: Real-time search with instant results
- **Shopping Cart**: Add, remove, and manage cart items
- **Order Management**: Track order status and history
- **User Profile**: Manage personal information and addresses
- **Favorites**: Save preferred products for later
- **Payment Integration**: Secure payments via Stripe

### 🔧 Service Features (Mobile App)

- **AR Area Measurement**: Real-time room measurement using ARCore
- **AI Recommendation System**: Furniture suggestions based on room photos
- **3D Space Visualization**: View 3D models of furniture
- **Carpenter Services**: Connect with professional carpenters

### 👨‍🔧 Workshop Dashboard (Mobile App)

- **Order Calendar**: Schedule and manage carpenter appointments
- **Analytics Dashboard**: View sales performance and metrics
- **Profile Management**: Manage carpenter profile and services
- **Order Processing**: Handle incoming service orders

## 🛠️ Technical Stack

### Mobile Development Stack

| Technology | Usage | Version |
|------------|-------|---------|
| **Flutter** | Cross-platform mobile development | 3.x |
| **Dart** | Programming language | 3.x |
| **BLoC/Cubit** | State management pattern | ^9.1.0 |
| **GetIt** | Dependency injection | ^8.0.3 |
| **Hive** | Local database storage | ^2.2.3 |
| **Dio** | HTTP client for API calls | ^5.8.0 |
| **Stripe** | Payment processing | ^11.1.0 |
| **ARCore Plugin** | Augmented reality features | ^0.0.9 |
| **Model Viewer** | 3D model visualization | ^1.8.0 |
| **YOLOv8** | AI object detection | Python integration |

### Architecture & Design

- **Clean Architecture**: Three-tier design with clear separation of concerns
- **MVVM Pattern**: Model-View-ViewModel for UI layer
- **Repository Pattern**: Data access abstraction
- **Use Cases**: Business logic encapsulation
- **Material Design 3**: Modern UI/UX guidelines
- **Responsive Design**: Adaptive layouts for all screen sizes

### Key Dependencies

```yaml
# State Management
flutter_bloc: ^9.1.0
bloc: ^9.0.0
get_it: ^8.0.3

# UI & Styling
flutter_screenutil: ^5.9.3
google_fonts: ^6.2.1
flutter_svg: ^2.0.17
cached_network_image: ^3.4.1

# Navigation
convex_bottom_bar: ^3.2.0

# Data & Storage
hive: ^2.2.3
hive_flutter: ^1.1.0
dio: ^5.8.0+1

# Payment
flutter_stripe: ^11.1.0

# AR/3D Features
arcore_flutter_plugin: ^0.0.9
model_viewer_plus: ^1.8.0

# AI Integration
camera: ^0.10.5
image_picker: ^1.1.2

# Utilities
dartz: ^0.10.1
permission_handler: ^11.3.1
```

## 📁 Project Structure

The project follows Clean Architecture principles with a clear separation of concerns:

```
lib/
├── Config/                     # App configuration and setup
│   ├── Routes/                 # Navigation and routing
│   ├── Themes/                 # Light and dark themes
│   ├── Cubit/                  # Global state management
│   └── app_config.dart         # App constants and settings
├── Core/                       # Core infrastructure
│   ├── Models/                 # Shared data models
│   ├── Resources/              # Colors, constants, assets
│   ├── Services/               # External services
│   │   ├── Payment/Strip/      # Stripe payment integration
│   │   └── ServiceLocator/     # Dependency injection
│   ├── Storage/                # Data persistence
│   │   ├── Local/              # Hive local storage
│   │   └── Remote/             # API services
│   └── Utils/                  # Utility functions and widgets
├── Features/                   # Feature modules
│   ├── Global/                 # Global features
│   │   ├── Authentication/     # Login/Register
│   │   ├── Boarding/           # Onboarding screens
│   │   └── Splash/             # Splash screen
│   ├── App/                    # Main app features
│   │   ├── common/             # Shared app features
│   │   │   ├── Home/           # Home screen
│   │   │   ├── Explore/        # Product categories
│   │   │   ├── Search/         # Search functionality
│   │   │   ├── Cart/           # Shopping cart
│   │   │   ├── Orders/         # Order management
│   │   │   ├── Favorite/       # Favorites
│   │   │   ├── Main/           # Main navigation
│   │   │   ├── ProductsDetails/# Product details
│   │   │   └── SpaceOptimizer/ # 3D visualization
│   │   ├── Service/            # Service features
│   │   │   ├── AreaMeasurement/# AR measurement
│   │   │   ├── Recommendation/ # AI recommendations
│   │   │   └── Carpenter/      # Carpenter services
│   │   ├── User/               # User profile
│   │   └── WorkshopDashboard/  # Carpenter dashboard
│   └── Admin/                  # Admin features
└── generated/                  # Generated files (localization)
```

## � Mobile App Screens

The mobile application consists of several key screen categories designed for optimal user experience:

### 🚀 Core Navigation Screens

- **Splash Screen**: App initialization and loading
- **Onboarding**: Interactive app introduction and feature walkthrough
- **Authentication**: Login and registration with social media integration

### 🏠 Main Application Screens

- **Home**: Main dashboard with featured products and quick access
- **Explore**: Category browsing with advanced filtering options
- **Search**: Smart search with real-time results and suggestions
- **Scan**: Camera-based product scanning and AR features
- **Profile**: User account management and settings

### 🛠️ Service Screens (Mobile App)

- **Area Measurement**: AR-powered room measurement tool
- **Recommendation**: AI furniture recommendation system
- **3D Viewer**: 3D model visualization screen

### 📋 Management Screens (Mobile App)

- **Orders**: Order tracking, history, and management
- **Cart**: Shopping cart with item management
- **Favorites**: Saved products and wishlist
- **Payment**: Secure payment processing with Stripe

## 🤖 Implemented Mobile Features

The mobile app includes the following implemented features:

### 📐 AR Area Measurement

- **Point-based Measurement**: Tap to place measurement points in AR
- **Real-time Calculation**: Instant area calculations
- **Multiple Units**: Support for cm² and ft² measurements
- **ARCore Integration**: Uses ARCore for Android AR functionality

### 🧠 AI Recommendation System

- **Photo Upload**: Upload room photos for analysis
- **Room Type Selection**: Choose from predefined room types
- **Dimension Input**: Specify room length and width
- **Product Suggestions**: Display recommended furniture products

### 🎨 3D Visualization

- **3D Model Viewer**: View furniture in 3D using model_viewer_plus
- **AR Preview**: Place 3D models in real space
- **Interactive Controls**: Rotate, zoom, and pan 3D models

## �🔧 Core Features Deep Dive

### 🏠 Home Screen

The home screen serves as the main entry point featuring:

- **Featured Products**: AI-curated highlighted furniture items
- **Popular Products**: Trending items based on user interactions and AI analysis
- **Category Navigation**: Quick access to product categories
- **Search Integration**: Instant search functionality with AI suggestions
- **User Profile Access**: Quick access to user settings and preferences

### 🔍 Explore & Search

Advanced product discovery system:

- **Category Filtering**: Browse by furniture categories
- **Real-time Search**: Instant search results as you type
- **Product Grid**: Responsive grid layout for product display
- **Caching System**: Optimized performance with cached data

### 🛒 Shopping Cart & Orders

Complete e-commerce functionality:

- **Cart Management**: Add, remove, and update quantities
- **Order Tracking**: Real-time order status updates
- **Order History**: Complete purchase history
- **Coupon System**: Apply and manage discount coupons

### 💳 Payment System

Secure payment processing with Stripe:

- **Payment Intent Creation**: Secure payment initialization
- **Ephemeral Keys**: Temporary authentication for payments
- **Payment Sheet**: Native payment UI
- **Transaction Security**: PCI-compliant payment processing

## 🔄 State Management Architecture

The app uses **BLoC/Cubit** pattern for state management with the following structure:

### Global State Management

- **SettingsCubit**: App-wide settings (theme, language, internet connectivity)
- **MainCubit**: Bottom navigation and main app flow
- **UserCubit**: User authentication and profile management

### Feature-Specific State Management

- **HomeCubit**: Home screen data and featured products
- **ExploreCubit**: Category browsing and product filtering
- **SearchCubit**: Search functionality and results
- **CartCubit**: Shopping cart operations
- **OrdersCubit**: Order management and tracking
- **AuthenticationCubit**: Login and registration flows
- **AreaMeasurementCubit**: AR measurement functionality
- **RecommendationCubit**: AI recommendation system
- **DashboardCubit**: Workshop dashboard navigation

### Service Locator Pattern

The app uses **GetIt** for dependency injection:

```dart
// Service registration
sl.registerLazySingleton<AuthenticationRepoImpl>(() => AuthenticationRepoImpl());
sl.registerFactory<HomeCubit>(() => HomeCubit(
  sl<GetFeaturedUseCase>(),
  sl<GetPopularUseCase>(),
  sl<ProductDetailsUseCase>(),
));
```

## 🌐 API Integration

### Base API Configuration

- **Base URL**: Configurable API endpoint
- **Authentication**: Bearer token-based authentication
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Caching**: Local caching for improved performance

### Data Models

The app uses strongly-typed data models with JSON serialization:

- **UserDataModel**: User profile and authentication data
- **ReviewProductsModel**: Product information and details
- **AllCategories**: Product category data
- **PaymentModel**: Stripe payment integration models

## 🎨 UI/UX Design System

### Design Principles

- **Material Design**: Following Material Design 3 guidelines
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Internationalization**: Arabic and English language support

### Color Scheme

- **Primary Colors**: Custom brand colors
- **Grayscale**: Comprehensive grayscale palette
- **Semantic Colors**: Success, error, warning, and info colors

### Typography

- **Google Fonts**: Custom font integration
- **Responsive Text**: Screen-size adaptive typography
- **Accessibility**: High contrast and readable text

### Components

- **Custom Widgets**: Reusable UI components
- **Animation Support**: Smooth transitions and micro-interactions
- **Loading States**: Skeleton loading and progress indicators

## 🚀 Getting Started

### Prerequisites

- **Flutter SDK**: Version 3.x or higher
- **Dart SDK**: Version 3.x or higher
- **Android Studio** or **VS Code** with Flutter extensions
- **Xcode** (for iOS development)
- **Android SDK**: API level 21 or higher

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-username/zan.git
   cd zan
   ```

2. **Install dependencies**

   ```bash
   flutter pub get
   ```

3. **Configure API Keys**

   Create a `.env` file in the root directory and add your API keys:

   ```env
   STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   API_BASE_URL=your_api_base_url
   ```

4. **Run the app**

   ```bash
   # For development
   flutter run

   # For release
   flutter run --release
   ```

### Platform-Specific Setup

#### Android

1. **Minimum SDK**: API level 21 (Android 5.0)
2. **Target SDK**: API level 34
3. **Permissions**: Camera, Internet, Storage
4. **ARCore**: Ensure ARCore is supported on target devices

#### iOS

1. **Minimum iOS**: 12.0
2. **Permissions**: Camera, Photo Library
3. **ARKit**: Ensure ARKit is supported on target devices

## 📱 Build & Deployment

### Android Build

```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### iOS Build

```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release
```

## 🔧 Development Guidelines

### Code Style

- Follow **Dart Style Guide**
- Use **flutter_lints** for code analysis
- Implement **Clean Architecture** principles
- Write comprehensive **documentation**

### Git Workflow

1. Create feature branches from `develop`
2. Use conventional commit messages
3. Submit pull requests for code review
4. Ensure all tests pass before merging

### State Management Best Practices

- Use **Cubit** for simple state management
- Use **BLoC** for complex business logic
- Implement proper error handling
- Use **Either** type for error handling with **dartz**

## 🌍 Internationalization

The app supports multiple languages:

- **English** (en)
- **Arabic** (ar)

### Adding New Languages

1. Add language code to `supportedLocales`
2. Create new `.arb` file in `lib/l10n/`
3. Run `flutter gen-l10n` to generate translations

## 🔐 Security Features

- **Token-based Authentication**: Secure JWT token management
- **Local Storage Encryption**: Sensitive data encryption with Hive
- **API Security**: HTTPS-only communication
- **Payment Security**: PCI-compliant Stripe integration
- **Permission Management**: Granular app permissions

## 📊 Performance Optimization

- **Image Caching**: Efficient image loading with `cached_network_image`
- **Lazy Loading**: On-demand data loading
- **State Persistence**: Maintain app state across sessions
- **Memory Management**: Proper disposal of resources
- **Network Optimization**: Request caching and retry mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🎓 Academic Information

This project was developed for academic purposes as part of a graduation project at:

**Future Academy - Higher Future Institute for Specialized Technological Studies**

- **School**: Computer Science Department
- **Academic Year**: 2024-2025
- **Project Type**: Graduation Project
- **Supervisor**: Dr. Ibrahim Abd-Allatif

### 🏆 Project Highlights

- **AI Integration**: Advanced machine learning models for furniture detection and recommendations
- **AR Technology**: Real-time augmented reality for space measurement and visualization
- **Cross-platform Development**: Single codebase for both Android and iOS
- **Modern Architecture**: Clean Architecture with MVVM pattern implementation
- **Industry-ready Solution**: Addresses real-world problems in carpentry and furniture industry

## 📄 License

This project was developed for academic purposes at **Future Academy - Higher Future Institute for Specialized Technological Studies**, School of Computer Science, May 2025.

## 👥 Development Team

- **Samuael Adel Fareed** - Project Lead & Backend Developer
- **Mahmoud Elsayed Khiralla** - Backend Developer & API Integration
- **Ziad Mohamed Elnagar** - Security & Penetration Testing
- **Maha Abdelmoneam Mohamed** - AI/ML Developer & Computer Vision
- **Ahmed Mohamed Ahmed** - Frontend Developer & Web Interface
- **Felopater Sameh Salama** - Mobile Application Developer (Flutter)
- **Supervisor**: Dr. Ibrahim Abd-Allatif - Academic Supervisor

## 📞 Documentation

- [Project Wiki]

---

**Built with ❤️ using Flutter**
