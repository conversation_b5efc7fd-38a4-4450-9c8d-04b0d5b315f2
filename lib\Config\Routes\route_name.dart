class AppRoutes {
  static const String splash = '/';
  static const String boarding = '/boarding';
  static const String authentication = '/authentication';
  static const String main = '/main';
  static const String search = '/search';
  static const String language = '/language';
  static const String card = '/card';
  static const String cart = '/cart';
  static const String security = '/security';
  static const String notification = '/notification';
  static const String info = '/info';
  static const String favorite = '/favorite';
  static const String productDetails = '/productDetails';
  static const String screen3D = '/screen3D';
  static const String areaMeasurementScreen = '/AreaMeasurementScreen';
  static const String userEditeScreen = '/userEditeScreen';
  static const String changePasswordScreen = '/changePasswordScreen';
  static const String selectLocation = '/selectLocation';
  static const String addAddressScreen = '/addAddressScreen';
  static const String carpenterScreen = '/carpenter';
  static const String chatPage = '/chat';
  static const String recommendation = '/recommendation';
  static const String dashBoredMainScreen = '/dashBoredMainScreen';
  static const String imageViewer = '/imageViewer';
  static const String imageView = '/ImageView';
  static const String calenderOrdersScreen = '/CalenderOrdersScreen';
  static const String resultRecommendationScreen =
      '/ResultRecommendationScreen';
  static const String seeAllPage = '/SeeAllPage';
  static const String dialogTestPage = '/dialogTestPage';
  static const String backConfirmationExample = '/backConfirmationExample';
  static const String orderDetailsPage = '/orderDetailsPage';
  static const String orderScreen = '/OrderScreen';
}
