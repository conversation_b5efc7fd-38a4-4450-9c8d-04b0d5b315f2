import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../data/Model/faqs_model.dart';
import '../../data/Model/message.dart';
import '../../data/Model/send_message.dart';
import '../../domain/UseCase/get_faqs_use_case.dart';
import '../../domain/UseCase/get_messages_use_case.dart';
import '../../domain/UseCase/send_messages_use_case.dart';

part 'chat_ai_state.dart';

class ChatAiCubit extends Cubit<ChatAiStateModel> {
  ChatAiCubit(this.faqs, this.getMessages, this.sendMessages)
      : super(ChatAiStateModel());

  final GetFaqsUseCase faqs;
  final GetMessagesUseCase getMessages;
  final SendMessagesUseCase sendMessages;




  Future<void> init() async {
    emit(state.copyWith(isLoading: true));
    await getFaqs();
    await getMessage();
    emit(state.copyWith(isLoading: false));
  }

  Future<void> getFaqs() async {
    final response = await faqs.call();
    response.fold((failure) async {
      emit(state.copyWith(faqs: [], message: failure.errMessage));
    }, (data) async {
      emit(state.copyWith(faqs: data));
    });
  }

  Future<void> getMessage() async {
    final response = await getMessages.call();
    response.fold((failure) async {
      emit(state.copyWith(messages: [], message: failure.errMessage));
    }, (data) async {
      emit(state.copyWith(
          messages: data.messages.reversed.toList(), isAuth: data.isAuthenticated));
    });
  }

  Future<void> sendMessage(SendMessage message) async {
    final userMessage = MessageModel(
      id: DateTime.now().millisecondsSinceEpoch,
      role: 'user',
      content: message.message,
      createdAt: DateTime.now(),
    );
    final loadingMessage = MessageModel(
      id: DateTime.now().millisecondsSinceEpoch + 1,
      role: 'bot',
      content: '.....',
      createdAt: DateTime.now(),
    );
    final updatedMessages = List<MessageModel>.from(state.messages?.reversed.toList() ?? [])..add(userMessage)..add(loadingMessage);
    emit(state.copyWith(messages: updatedMessages.reversed.toList(), isTyping: true));
    final response = await sendMessages.call(message);
    response.fold((failure) async {
      emit(state.copyWith(message: failure.errMessage, isTyping: false));
    }, (data) async {
      final messages = List<MessageModel>.from(state.messages?.reversed.toList() ?? []);
      if (data.isNotEmpty) {
        messages.removeLast();
        messages.add(data.last);
      }
      emit(state.copyWith(
        messages: messages.reversed.toList(),
        isTyping: false,
      ));
    });
  }

}
