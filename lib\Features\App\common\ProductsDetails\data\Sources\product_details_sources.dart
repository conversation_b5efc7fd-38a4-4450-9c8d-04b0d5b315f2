import '../../../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../../../Core/Storage/Remote/api_service.dart';

import '../entities/product_details_model.dart';
import '../entities/add_to_cart.dart';

class ProductDetailsSources {
  static Future<ViewProductDetails> getProductDetails(
      {required int id}) async {
    final response =
        await DioHelper.getData(path: "${ApiEndpoints.productDetails}+$id");

    if (response.statusCode == 200) {
      final product = ViewProductDetails.fromJson(
          (response.data["data"]));
      return product;
    } else {
      throw Exception('Failed to load product details');
    }
  }

  static Future<bool> addToCart({
    required AddToCart item,
  }) async {
    final response = await DioHelper.postData(
      data: item.toMap(),
      path: ApiEndpoints.addCart,
    );

    if (response.statusCode == 200) {
      final item = true;
      return item;
    } else {
      throw Exception('Failed to load product details');
    }
  }

  static Future<Review> addReview({required int rating,  String? comment, required int productId}) async {
    final response = await DioHelper.postData(
      data: {
        "rating": rating,
        "comment": comment
      },
      path: "${ApiEndpoints.product}/$productId${ApiEndpoints.productReview}",
    );
      return Review.fromJson(response.data["data"]);
  }
}
