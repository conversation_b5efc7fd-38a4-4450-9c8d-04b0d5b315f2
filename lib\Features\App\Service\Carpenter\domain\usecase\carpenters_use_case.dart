import 'package:dartz/dartz.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Models/carpenters_model.dart';
import '../../data/Models/get_carpenter_available_model.dart';
import '../repository/carpenter_services_repo_impl.dart';

class CarpentersUseCase {
  final CarpenterServicesRepoImpl repoImpl ;

  CarpentersUseCase(this.repoImpl);

    Future<Either<Failure, List<CarpentersModel>>> call(PostCarpenterAvailableModel post)async => await repoImpl.getCarpenters( post);
}
