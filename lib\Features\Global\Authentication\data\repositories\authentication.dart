import 'package:dartz/dartz.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../Models/login_model.dart';
import '../Models/register_model.dart';
import '../Models/user_data_model.dart';

abstract class AuthenticationRepo {
  Future<Either<Failure, UserDataModel>> loginByEmail(LoginModel loginModel);

  Future<Either<Failure, bool>> registerByEmail(RegisterModel registerModel);

  }
