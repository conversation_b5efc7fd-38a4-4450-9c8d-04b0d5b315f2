import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../data/Models/create_orders.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Models/carpenters_model.dart';

import '../../data/Models/get_all_services_model.dart';

import '../../data/Models/get_carpenter_available_model.dart';
import '../../data/repositories/carpenter_services_repo.dart';
import '../../data/sources/carpenter_services_sources.dart';

class CarpenterServicesRepoImpl extends CarpenterServicesRepo{
  @override
  Future<Either<Failure, List<GetAllServicesModel>>> getAllServices() async{
    try {
      final response = await CarpenterServicesSources.getAllServices();
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, List<CarpentersModel>>> getCarpenters(PostCarpenterAvailableModel post)async{
    try {
      final response = await CarpenterServicesSources.getCarpenters(post);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, bool>> createOrders(CreateOrdersModel order) async{
    try {
      final response = await CarpenterServicesSources.createOrder(order);
      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

}
