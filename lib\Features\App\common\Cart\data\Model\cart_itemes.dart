import '../../../ProductsDetails/data/entities/product_details_model.dart';

class CartItemsModel {
  CartItemsModel({
    required this.cartItems,
    required this.summary,
  });

  final List<Item> cartItems;
  final Summary? summary;

  factory CartItemsModel.fromJson(Map<String, dynamic> json) {
    return CartItemsModel(
      cartItems: json["cart_items"] == null
          ? []
          : List<Item>.from(
              json["cart_items"]!.map((x) => Item.fromJson(x))),
      summary:
          json["summary"] == null ? null : Summary.fromJson(json["summary"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "cart_items": cartItems.map((x) => x.toJson()).toList(),
        "summary": summary?.toJson(),
      };
}

class Item {
  Item({
    required this.id,
    required this.userId,
    required this.productId,
    required this.colorId,
    required this.sizeId,
    required this.quantity,
    required this.isFromPackage,
    required this.packageId,
    required this.packagePrice,
    required this.createdAt,
    required this.updatedAt,
    required this.product,
    required this.color,
    required this.size,
  });

  final int id;
  final int userId;
  final int productId;
  final int colorId;
  final int sizeId;
  final int quantity;
  final int isFromPackage;
  final dynamic packageId;
  final dynamic packagePrice;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final ViewProductDetails? product;
  final ColorModel? color;
  final SizeModel? size;

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json["id"] ?? 0,
      userId: json["user_id"] ?? 0,
      productId: json["product_id"] ?? 0,
      colorId: json["color_id"] ?? 0,
      sizeId: json["size_id"] ?? 0,
      quantity: json["quantity"] ?? 0,
      isFromPackage: json["is_from_package"] ?? 0,
      packageId: json["package_id"],
      packagePrice: json["package_price"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      product: json["product"] == null
          ? null
          : ViewProductDetails.fromJson(
              json["product"],
            ),
      color: json["color"] == null ? null : ColorModel.fromJson(json["color"]),
      size: json["size"] == null ? null : SizeModel.fromJson(json["size"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "product_id": productId,
        "color_id": colorId,
        "size_id": sizeId,
        "quantity": quantity,
        "is_from_package": isFromPackage,
        "package_id": packageId,
        "package_price": packagePrice,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "product": product?.toJson(),
        "color": color?.toJson(),
        "size": size?.toJson(),
      };
}



class Summary {
  Summary({
    required this.subtotal,
    required this.couponDiscount,
    required this.deliveryCharge,
    required this.grandTotal,
    required this.couponCode,
  });

  final int subtotal;
  final int couponDiscount;
  final int deliveryCharge;
  final int grandTotal;
  final dynamic couponCode;

  factory Summary.fromJson(Map<String, dynamic> json) {
    return Summary(
      subtotal: json["subtotal"] ?? 0,
      couponDiscount: json["coupon_discount"] ?? 0,
      deliveryCharge: json["delivery_charge"] ?? 0,
      grandTotal: json["grand_total"] ?? 0,
      couponCode: json["coupon_code"],
    );
  }

  Map<String, dynamic> toJson() => {
        "subtotal": subtotal,
        "coupon_discount": couponDiscount,
        "delivery_charge": deliveryCharge,
        "grand_total": grandTotal,
        "coupon_code": couponCode,
      };
}
