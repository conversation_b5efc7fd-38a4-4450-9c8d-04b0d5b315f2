import 'package:vector_math/vector_math_64.dart' as math;

class AreaMeasurementState  {
  final List<math.Vector3> points;
  final double area;
  final bool isMeasuringSmallArea;
  final bool isPhoneStable;
  final String instructionText;
  final String errorMessage;
  final bool showError;

  const AreaMeasurementState({
    required this.points,
    required this.area,
    required this.isMeasuringSmallArea,
    required this.isPhoneStable,
    required this.instructionText,
    required this.errorMessage,
    required this.showError,
  });

  factory AreaMeasurementState.initial() => const AreaMeasurementState(
    points: [],
    area: 0.0,
    isMeasuringSmallArea: false,
    isPhoneStable: false,
    instructionText: "حرك الهاتف لاكتشاف الأسطح ثم انقر لتحديد الزوايا",
    errorMessage: "",
    showError: false,
  );

  AreaMeasurementState copyWith({
    List<math.Vector3>? points,
    double? area,
    bool? isMeasuringSmallArea,
    bool? isPhoneStable,
    String? instructionText,
    String? errorMessage,
    bool? showError,
  }) {
    return AreaMeasurementState(
      points: points ?? this.points,
      area: area ?? this.area,
      isMeasuringSmallArea: isMeasuringSmallArea ?? this.isMeasuringSmallArea,
      isPhoneStable: isPhoneStable ?? this.isPhoneStable,
      instructionText: instructionText ?? this.instructionText,
      errorMessage: errorMessage ?? this.errorMessage,
      showError: showError ?? this.showError,
    );
  }

  // @override
  // List<Object?> get props => [
  //   points,
  //   area,
  //   isMeasuringSmallArea,
  //   isPhoneStable,
  //   instructionText,
  //   errorMessage,
  //   showError,
  // ];
}