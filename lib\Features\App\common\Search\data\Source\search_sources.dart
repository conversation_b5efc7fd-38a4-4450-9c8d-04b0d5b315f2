import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../../../Core/Storage/Remote/api_service.dart';

class SearchSources {
  static Future<List<ReviewProductsModel>> searchProducts() async {
      final response = await DioHelper.getData(
        path: ApiEndpoints.product,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data["data"]["products"];

        if (data is List) {
          return data
              .map((item) => ReviewProductsModel.fromJson(
            item
          ))
              .toList();
        } else {
          return [];
        }
      } else {
        return [];
      }

  }
// Future<List<ProductPreviewEntities>> searchProductsByFilters()async{
//   final response = await DioHelper.getData(
//     path: ApiEndpoints.product,
//   );
//   return (response.data["data"] as List)
//       .map(
//         (item) => ProductPreviewEntities.fromJson(item),
//       )
//       .toList();
// }
}
