import '../Models/create_orders.dart';

import '../../../../../../Core/Storage/Remote/api_endpoints.dart';

import '../../../../../../Core/Storage/Remote/api_service.dart';
import '../Models/carpenters_model.dart';
import '../Models/get_all_services_model.dart';
import '../Models/get_carpenter_available_model.dart';

class CarpenterServicesSources {
  static Future<List<GetAllServicesModel>> getAllServices() async {
    final response =
        await DioHelper.getData(path: ApiEndpoints.allCarpentryServices);

    return (response.data["data"]["services"] as List)
        .map((e) => GetAllServicesModel.fromJson(e))
        .toList();
  }

  static Future<List<CarpentersModel>> getCarpenters(
      PostCarpenterAvailableModel post) async {
    final response = await DioHelper.postData(
        data: post.toMap(), path: ApiEndpoints.carpenters);
    return (response.data["data"]["carpenters"] as List)
        .map((e) => CarpentersModel.fromJson(e))
        .toList();
  }

  static Future<bool> createOrder(CreateOrdersModel order) async {
    final response = await DioHelper.postData(
        data: order.toMap(), path: ApiEndpoints.createOrderCarpenters);
    return response.statusCode == 200 ? true : false;
  }
}
