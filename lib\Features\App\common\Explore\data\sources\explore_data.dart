import '../../../../../../Core/Storage/Remote/api_service.dart';

import '../Model/all_categories.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_endpoints.dart';

class ExploreData {
  static Future<List<AllCategories>> getAllCategories() async {
    final response = await DioHelper.getData(
      path: ApiEndpoints.allCategory,
    );
    return (response.data["data"] as List)
        .map(
          (item) => AllCategories.fromJson(item),
        )
        .toList();
  }

  static Future<List<ReviewProductsModel>> getProductsCategory(
      {required int id}) async {
    final response = await DioHelper.getData(
      path:
          "${ApiEndpoints.getProductsByCategories}$id/${(ApiEndpoints.product)}",
    );
    return (response.data["data"]["products"] as List)
        .map(
          (item) => ReviewProductsModel.fromJson(
            item,
          ),
        )
        .toList();
  }
}
