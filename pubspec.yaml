name: zan
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl_utils: ^2.8.8

  # ------------------ Core Packages ------------------
  flutter_bloc: ^9.1.0
  bloc: ^9.0.0
  get_it: ^8.0.3
  dartz: ^0.10.1

  # ------------------ UI & Styling ------------------
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.17
  flutter_screenutil: ^5.9.3
  font_awesome_flutter: ^10.8.0
  animations: ^2.0.11
  cached_network_image: ^3.4.1
  top_snackbar_flutter: ^3.2.0
  rating_summary: ^1.0.4
  fl_chart: ^0.71.0

  # ------------------ Navigation & Bottom Bars ------------------
  convex_bottom_bar: ^3.2.0
  buttons_tabbar: ^1.3.15

  # ------------------ Screens & Onboarding ------------------
  introduction_screen: ^3.1.17
  tutorial_coach_mark: ^1.2.13

  # ------------------ Data & Storage ------------------
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  dio: ^5.8.0+1

  # ------------------ Loading & Effects ------------------
  skeletonizer: ^2.0.1

  # ------------------ Utilities & Tools ------------------
  flutter_launcher_icons: ^0.14.3
  flutter_expandable_fab: ^2.4.1
  choice: ^2.3.2
  intl: ^0.20.2
  translator: ^1.0.3+1

  # ------------------ AR & 3D ------------------
  model_viewer_plus: ^1.9.3
  ar_flutter_plugin_updated: ^0.0.1
  vector_math: ^2.1.4

  # ------------------ Connectivity & Meta ------------------
  meta: ^1.16.0
  connectivity_plus: ^6.1.3
  table_calendar: ^3.1.3

  # ------------------ Maps & Location ------------------
  google_maps_flutter: ^2.12.1
  location: ^8.0.0
  geocoding: ^3.0.0

  # ------------------ Chat & Media ------------------
  flutter_chat_ui: ^1.6.15
  flutter_chat_types: ^3.6.2
  flutter_stripe: ^11.5.0
  image_picker: ^1.1.2
  permission_handler: ^11.3.1
  photo_view: ^0.15.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  build_runner: ^2.4.15
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - Assets/Svg/
    - Assets/test_images/
    - Assets/Png/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_intl:
  enabled: true

flutter_launcher_icons:
  android: "launcher_icon"
  image_path: "Assets/Png/ic_launcher_foreground.png"
  adaptive_icon_background: "Assets/Png/ic_launcher_background.png"
  adaptive_icon_foreground: "Assets/Png/ic_launcher_foreground.png"

