
class CouponModel {
  CouponModel({
    required this.id,
    required this.code,
    required this.type,
    required this.value,
    required this.minOrderAmount,
    required this.expiresAt,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String code;
  final String type;
  final String value;
  final String minOrderAmount;
  final DateTime? expiresAt;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory CouponModel.fromJson(Map<String, dynamic> json) {
    return CouponModel(
      id: json["id"] ?? 0,
      code: json["code"] ?? "",
      type: json["type"] ?? "",
      value: json["value"] ?? "",
      minOrderAmount: json["min_order_amount"] ?? "",
      expiresAt: DateTime.tryParse(json["expires_at"] ?? ""),
      status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "type": type,
    "value": value,
    "min_order_amount": minOrderAmount,
    "expires_at": expiresAt?.toIso8601String(),
    "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };
}