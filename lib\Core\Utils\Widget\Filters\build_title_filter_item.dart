import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../Resources/app_fonts.dart';

class BuildTitleFilterItem extends StatelessWidget {
  final String title ;
  const BuildTitleFilterItem({
    super.key, required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16).r,
      child: Text(
        title,
        style: AppTextStyles.bodyLargeSemiBold,
      ),
    );
  }
}
