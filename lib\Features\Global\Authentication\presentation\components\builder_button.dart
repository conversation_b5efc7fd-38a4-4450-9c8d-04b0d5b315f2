// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
//
// import '../../../../Core/Resources/colors.dart';
//
// class CustomElevatedButton extends StatelessWidget {
//   final String text;
//   final VoidCallback onTap;
//   final double? width;
//   final Color? colorText;
//
//   const CustomElevatedButton({
//     super.key,
//     required this.text,
//     required this.onTap,
//     this.width,
//     this.colorText,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return ElevatedButton(
//       style: ButtonStyle(
//           alignment: Alignment.center,
//           backgroundColor: WidgetStatePropertyAll(
//             AppColors.colorPrimary,
//           ),
//           fixedSize: WidgetStatePropertyAll(
//             Size.fromWidth(
//               width ?? .9.sw,
//             ),
//           )),
//       onPressed: onTap,
//       child: Text(
//         textAlign: TextAlign.center,
//         text,
//         style: context.getTextStyle.bodyMedium?.copyWith(color: colorText),
//       ),
//     );
//   }
// }

// boarding