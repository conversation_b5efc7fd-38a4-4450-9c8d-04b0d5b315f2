import 'package:meta/meta.dart';

import '../../../ProductsDetails/data/entities/product_details_model.dart';
import '../../../../../../Core/Models/review_products_model.dart';

@immutable
class HomeStateModel {
  final String errorMessage ;
  final List<ReviewProductsModel> productsFeatured;
  final List<ReviewProductsModel> productsPopular;
  final ViewProductDetails? productDetails;
  final List<ReviewProductsModel> favoriteProductDetails;
  final Map<String, bool> isLoading;

  const HomeStateModel({
    this.productsFeatured = const [],
    this.productsPopular = const [],
    this.isLoading = const {},
    this.errorMessage ="",
    this.productDetails,
    this.favoriteProductDetails = const [],
  });

  HomeStateModel copyWith({
    List<ReviewProductsModel>? productsFeatured,
    String? errorMessage,
    List<ReviewProductsModel>? productsPopular,
    Map<String, bool>? isLoading,
    ViewProductDetails? productDetails,
    List<ReviewProductsModel>? favoriteProductDetails,
  }) {
    return HomeStateModel(
        productDetails: productDetails ?? this.productDetails,
        productsFeatured: productsFeatured ?? this.productsFeatured,
        productsPopular: productsPopular ?? this.productsPopular,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        favoriteProductDetails:
        favoriteProductDetails ?? this.favoriteProductDetails,
        );
  }
}