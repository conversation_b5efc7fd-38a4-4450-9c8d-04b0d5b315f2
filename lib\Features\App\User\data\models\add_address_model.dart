class AddressModel {
  num? userId;
  String? name;
  String? phone;
  String? address;
  String? area;
  String? city;
  String? state;
  String? postalCode;
  bool? isDefault;
  String? updatedAt;
  String? createdAt;
  num? id;

  AddressModel(
    this.userId,
    this.name,
    this.phone,
    this.address,
    this.area,
    this.city,
    this.state,
    this.postalCode,
    this.isDefault,
    this.updatedAt,
    this.createdAt,
    this.id,
  );
  AddressModel.input(
      this.name,
      this.phone,
      this.address,
      this.area,
      this.city,
      this.state,
      this.postalCode,
      this.isDefault,
      );

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    return AddressModel(
      json['user_id'],
      json['name'],
      json['phone'],
      json['address_line'],
      json['area'],
      json['city'],
      json['state'],
      json['postal_code'],
      json['is_default'] == 1 ? true : false,
      json['updated_at'],
      json['created_at'],
      json['id'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'name': name,
      'phone': phone,
      'address_line': address,
      'area': area,
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'is_default': isDefault,
    };
  }

  AddressModel copyWith({
    num? userId,
    String? name,
    String? phone,
    String? address,
    String? area,
    String? city,
    String? state,
    String? postalCode,
    bool? isDefault,
    String? updatedAt,
    String? createdAt,
    num? id,
  }) {
    return AddressModel(
       userId ?? this.userId,
       name ?? this.name,
       phone ?? this.phone,
       address ?? this.address,
       area ?? this.area,
       city ?? this.city,
       state ?? this.state,
       postalCode ?? this.postalCode,
       isDefault ?? this.isDefault,
       updatedAt ?? this.updatedAt,
       createdAt ?? this.createdAt,
       id ?? this.id,
    );
  }
}
