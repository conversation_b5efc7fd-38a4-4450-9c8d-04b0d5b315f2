import 'package:dartz/dartz.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/entities/add_to_cart.dart';
import '../repository/product_details_repo_impl.dart';

class AddToCartUseCase {
  final ProductDetailsRepoImpl productDetailsRepoImpl;

  AddToCartUseCase({required this.productDetailsRepoImpl});

  Future<Either<Failure, bool>> call({required AddToCart item}) async {
    return await productDetailsRepoImpl.addToCart(
      item: item,
    );
  }

}
