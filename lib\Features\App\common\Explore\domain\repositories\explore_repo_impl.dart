import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../data/Model/all_categories.dart' show AllCategories;
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';

import '../../data/repositories/explore_repo.dart';
import '../../data/sources/explore_data.dart';

class ExploreRepoImpl extends ExploreRepo {
  @override
  Future<Either<Failure, List<AllCategories>>>
      getAllCategories() async {
    try {
      final response = await ExploreData.getAllCategories();

      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }

  @override
  Future<Either<Failure, List<ReviewProductsModel>>>
      getCategoriesDetails({required int id}) async {
    try {
      final response = await ExploreData.getProductsCategory(id: id);

      return right(response);
    } on DioException catch (e) {
      return Left(ServerFailure.fromDioError(e));
    } catch (e) {
      return Left(ServerFailure("Unknown error occurred"));
    }
  }
}
