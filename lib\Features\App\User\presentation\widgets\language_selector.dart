import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../Config/Cubit/settings_cubit.dart';
import '../../../../../Config/app_config.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_fonts.dart';
import '../../../../../Core/Resources/app_icons.dart';
import '../../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../../generated/l10n.dart';

class LanguageList extends StatelessWidget {
  const LanguageList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildLanguageItems(
        context,
        context.read<SettingsCubit>().state.locale,
      ),
    );
  }

  List<Widget> _buildLanguageItems(
      BuildContext context, Locale selectedLocale) {
    final supportedLocales = AppLocalizationDelegate().supportedLocales;

    return List.generate(supportedLocales.length, (index) {
      final locale = supportedLocales[index];
      bool isSelected = locale == selectedLocale;

      return Column(
        children: [
          InkWell(
            onTap: () async {
                context.read<SettingsCubit>().changeLanguage(locale);
                await LocalStorageService.setValue(
                  LocalStorageKeys.selectedLanguage,
                  locale.languageCode,
                );
              },
            child: Padding(
              padding: AppConfig.customPadding,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppConfig.languageNames[locale.languageCode] ??
                        locale.languageCode,
                    style: AppTextStyles.bodyLargeBold,
                  ),
                  if (isSelected) AppIcons.check,
                ],
              ),
            ),
          ),
          if (index != supportedLocales.length - 1)
            Padding(
              padding: AppConfig.customPadding,
              child: Divider(
                color: AppColors.grayscale30,
                thickness: 2,
              ),
            ),
        ],
      );
    });
  }
}
