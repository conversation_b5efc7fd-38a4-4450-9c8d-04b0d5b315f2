// import 'package:flutter/material.dart';
// import 'package:vector_math/vector_math_64.dart' as math;
//
// class DistanceLabel extends StatelessWidget {
//   final math.Vector3 point1;
//   final math.Vector3 point2;
//
//   const DistanceLabel({
//     super.key,
//     required this.point1,
//     required this.point2,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     // Calculate distance between points
//     final distance = (point2 - point1).length;
//     final displayDistance = distance < 1
//         ? "${(distance * 100).toStringAsFixed(1)} سم"
//         : "${distance.toStringAsFixed(2)} م";
//
//     // In an actual implementation, you would need to:
//     // 1. Calculate the midpoint position in 3D space
//     // 2. Project that position to 2D screen coordinates
//     // 3. Position this widget at those coordinates
//
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: Colors.black.withOpacity(0.7),
//         borderRadius: BorderRadius.circular(4),
//       ),
//       child: Text(
//         displayDistance,
//         style: const TextStyle(
//           color: Colors.white,
//           fontSize: 12,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }
// }