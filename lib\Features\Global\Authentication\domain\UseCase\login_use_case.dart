import 'package:dartz/dartz.dart';
import '../repository/authentication_repo_impl.dart';
import '../../data/Models/user_data_model.dart';

import '../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../../data/Models/login_model.dart';

class LoginUseCase {
  final AuthenticationRepoImpl authRepositoryImpl;

  LoginUseCase(this.authRepositoryImpl);

  Future<Either<Failure, UserDataModel>> call(LoginModel loginModel) async => await authRepositoryImpl.loginByEmail(loginModel);

}