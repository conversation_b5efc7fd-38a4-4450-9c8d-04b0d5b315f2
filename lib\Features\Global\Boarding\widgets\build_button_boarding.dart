import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Resources/app_colors.dart';

Widget buildButtonBoarding(String text) {
  return Container(
    width: 0.8.sw,
    height: 50.h,
    decoration: BoxDecoration(
      color: AppColors.primaryColor,
      borderRadius: BorderRadius.circular(25).r,
    ),
    alignment: Alignment.center,
    child: Text(text,
        maxLines: text.length,
        style: TextStyle(
            fontSize: 16.sp, fontWeight: FontWeight.bold, color: Colors.white)),
  );
}
