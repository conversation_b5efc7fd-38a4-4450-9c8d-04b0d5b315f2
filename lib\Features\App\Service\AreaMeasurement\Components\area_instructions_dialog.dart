import 'package:flutter/material.dart';

class AreaInstructionsDialog extends StatelessWidget {
  const AreaInstructionsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("تعليمات القياس"),
      content: const SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("1. حرك الهاتف لاكتشاف الأسطح (ستظهر كمناطق صفراء)"),
            SizedBox(height: 8),
            Text("2. انقر لوضع نقطة على كل زاوية من المساحة التي تريد قياسها"),
            SizedBox(height: 8),
            Text("3. يلزم 3 نقاط على الأقل لحساب المساحة"),
            SizedBox(height: 8),
            Text("4. حافظ على ثبات الهاتف للحصول على قياسات أكثر دقة"),
            SizedB<PERSON>(height: 8),
            Text("5. استخدم زر التراجع لإزالة آخر نقطة"),
            SizedBox(height: 8),
            Text("6. استخدم زر المسح لبدء قياس جديد"),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text("فهمت"),
        ),
      ],
    );
  }
}