class CouponsModel {
  CouponsModel({
    required this.coupon,
    required this.discount,
    required this.summary,
  });

  final Coupon? coupon;
  final String discount;
  final Summary? summary;

  factory CouponsModel.fromJson(Map<String, dynamic> json){
    return CouponsModel(
      coupon: json["coupon"] == null ? null : Coupon.fromJson(json["coupon"]),
      discount: json["discount"] ?? "",
      summary: json["summary"] == null ? null : Summary.fromJson(json["summary"]),
    );
  }

  Map<String, dynamic> toJson() => {
    "coupon": coupon?.toJson(),
    "discount": discount,
    "summary": summary?.toJson(),
  };

}

class Coupon {
  Coupon({
    required this.id,
    required this.code,
    required this.type,
    required this.value,
    required this.minOrderAmount,
    required this.expiresAt,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String code;
  final String type;
  final String value;
  final String minOrderAmount;
  final DateTime? expiresAt;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory Coupon.fromJson(Map<String, dynamic> json){
    return Coupon(
      id: json["id"] ?? 0,
      code: json["code"] ?? "",
      type: json["type"] ?? "",
      value: json["value"] ?? "",
      minOrderAmount: json["min_order_amount"] ?? "",
      expiresAt: DateTime.tryParse(json["expires_at"] ?? ""),
      status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "type": type,
    "value": value,
    "min_order_amount": minOrderAmount,
    "expires_at": expiresAt?.toIso8601String(),
    "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

}

class Summary {
  Summary({
    required this.subtotal,
    required this.couponDiscount,
    required this.deliveryCharge,
    required this.grandTotal,
  });

  final int subtotal;
  final String couponDiscount;
  final int deliveryCharge;
  final int grandTotal;

  factory Summary.fromJson(Map<String, dynamic> json){
    return Summary(
      subtotal: json["subtotal"] ?? 0,
      couponDiscount: json["coupon_discount"] ?? "",
      deliveryCharge: json["delivery_charge"] ?? 0,
      grandTotal: json["grand_total"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    "subtotal": subtotal,
    "coupon_discount": couponDiscount,
    "delivery_charge": deliveryCharge,
    "grand_total": grandTotal,
  };

}
