import 'package:flutter/material.dart';

import '../../../../Core/Resources/app_colors.dart';

class Loading extends StatelessWidget {
  const Loading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CircularProgressIndicator(
        color: AppColors.primaryColor.withAlpha(0x80),
        backgroundColor: Color.fromRGBO(255, 255, 255, .1),
        strokeCap: StrokeCap.round,
        strokeWidth: 8,
      ),
    );
  }
}
