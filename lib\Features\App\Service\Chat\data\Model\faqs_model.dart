class FaqsModel {
  FaqsModel({
    required this.id,
    required this.question,
    required this.priority,
    required this.category,
  });

  final int id;
  final String question;
  final int priority;
  final String category;

  factory FaqsModel.fromJson(Map<String, dynamic> json){
    return FaqsModel(
      id: json["id"] ?? 0,
      question: json["question"] ?? "",
      priority: json["priority"] ?? 0,
      category: json["category"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "question": question,
    "priority": priority,
    "category": category,
  };

}
