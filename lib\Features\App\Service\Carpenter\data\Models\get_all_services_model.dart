class GetAllServicesModel {
  GetAllServicesModel({
    required this.id,
    required this.categoryId,
    required this.name,
    required this.slug,
    required this.description,
    required this.image,
    required this.basePrice,
    required this.discountPercentage,
    required this.isFeatured,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.imageUrl,
    required this.category,
  });

  factory GetAllServicesModel.empty() => GetAllServicesModel(
        id: 0,
        categoryId: 0,
        name: "Name",
        slug: "",
        description: "",
        image: "",
        basePrice: "",
        discountPercentage: "",
        isFeatured: 0,
        status: 0,
        createdAt: null,
        updatedAt: null,
        imageUrl: "",
        category: null,
      );
  final int id;
  final int categoryId;
  final String name;
  final String slug;
  final String description;
  final String image;
  final String basePrice;
  final String discountPercentage;
  final int isFeatured;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String imageUrl;
  final Category? category;

  factory GetAllServicesModel.fromJson(Map<String, dynamic> json){
    return GetAllServicesModel(
      id: json["id"] ?? 0,
      categoryId: json["category_id"] ?? 0,
      name: json["name"] ?? "",
      slug: json["slug"] ?? "",
      description: json["description"] ?? "",
      image: json["image"] ?? "",
      basePrice: json["base_price"] ?? "",
      discountPercentage: json["discount_percentage"] ?? "",
      isFeatured: json["is_featured"] ?? 0,
      status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      imageUrl: json["image_url"] ?? "",
      category: json["category"] == null ? null : Category.fromJson(json["category"]),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "category_id": categoryId,
    "name": name,
    "slug": slug,
    "description": description,
    "image": image,
    "base_price": basePrice,
    "discount_percentage": discountPercentage,
    "is_featured": isFeatured,
    "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
    "image_url": imageUrl,
    "category": category?.toJson(),
  };

}

class Category {
  Category({
    required this.id,
    required this.name,
    required this.slug,
    required this.icon,
    required this.description,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  final int id;
  final String name;
  final String slug;
  final String icon;
  final String description;
  final int status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory Category.fromJson(Map<String, dynamic> json){
    return Category(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      slug: json["slug"] ?? "",
      icon: json["icon"] ?? "",
      description: json["description"] ?? "",
      status: json["status"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "slug": slug,
    "icon": icon,
    "description": description,
    "status": status,
    "created_at": createdAt?.toIso8601String(),
    "updated_at": updatedAt?.toIso8601String(),
  };

}
