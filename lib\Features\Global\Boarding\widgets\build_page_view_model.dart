import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:introduction_screen/introduction_screen.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_fonts.dart';
import '../../../../../Core/Utils/Widget/Images/build_image.dart';

PageViewModel buildPageViewModel({
  required String title,
  required String body,
  required String image,
}) {
  return PageViewModel(
    titleWidget: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          title,
          maxLines: title.length,
          textAlign: TextAlign.center,
          style: AppTextStyles.h4Bold.copyWith(color: AppColors.textColorWhite),
        ),
        10.verticalSpace,
        Text(
          body,
          maxLines: body.length,
          textAlign: TextAlign.center,
          style: AppTextStyles.bodyMediumSemiBold
              .copyWith(color: AppColors.grayscale50),
        ),
        30.verticalSpace
      ],
    ),
    bodyWidget: const SizedBox(),
    image: _buildBackgroundImage(image),
    decoration: _pageDecoration(),
  );
}

PageDecoration _pageDecoration() {
  return const PageDecoration(
    fullScreen: true,
    bodyFlex: 0,
    imagePadding: EdgeInsets.zero,
    contentMargin: EdgeInsets.zero,
  );
}

Widget _buildBackgroundImage(String assetName) {
  return Stack(
    fit: StackFit.expand,
    children: [
      Container(
        color: AppColors.backgroundColor.withAlpha((0.3 * 255).toInt()),
      ),
      BuildImageAssets(
        png: assetName,
        fit: BoxFit.cover,
        width: 1.sw,
        height: 1.sh,
      ),
    ],
  );
}
