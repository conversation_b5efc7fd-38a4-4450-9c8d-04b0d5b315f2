import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../../../../Config/Assets/images_test.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';


class CardCarpenter extends StatelessWidget {
  const CardCarpenter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          height: 150.h,
          child: Row(
            children: [
              Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 20.w),
                    height: 120.h,
                    width: 120.w,
                    decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(AppImagesTest.testCarpenter),
                        ),
                        boxShadow: [
                          BoxShadow(
                              color: AppColors.primaryColor,
                              spreadRadius: .5,
                              blurRadius: 5,
                              blurStyle: BlurStyle.outer),
                        ],
                        shape: BoxShape.circle),
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 25.w,
                    child: FaIcon(
                      FontAwesomeIcons.certificate,
                      color: Colors.green,
                    ),
                  )
                ],
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Name carpenter",
                        maxLines: 1, style: AppTextStyles.h6Bold),
                    Text(
                      "Full Address",
                      maxLines: 1,
                      style: AppTextStyles.bodySmallSemiBold.copyWith(
                        color: AppColors.grayscale60,
                      ),
                    ),
                    Expanded(
                        child: Text(
                          "Information From carpenter",
                          maxLines: 3,
                          style: AppTextStyles.bodyMediumBold,
                        )),
                    Expanded(
                      child: Row(
                        children: [
                          Row(
                            children: [
                              Icon(Icons.star, color: Colors.amberAccent),
                              Icon(Icons.star, color: Colors.amberAccent),
                              Icon(Icons.star, color: Colors.amberAccent),
                              Icon(Icons.star, color: Colors.amberAccent),
                              Icon(Icons.star_half,
                                  color: Colors.amberAccent),
                            ],
                          ),
                          Expanded(
                              child: Row(
                                mainAxisAlignment:
                                MainAxisAlignment.spaceEvenly,
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.message,
                                    size: 18,
                                    color: Colors.blueGrey,
                                  ),
                                  FaIcon(
                                    FontAwesomeIcons.locationPin,
                                    size: 18,
                                    color: Colors.blueGrey,
                                  ),
                                ],
                              ))
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
