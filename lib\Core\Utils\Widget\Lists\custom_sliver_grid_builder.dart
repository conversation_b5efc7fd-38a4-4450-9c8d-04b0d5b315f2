import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../Animations/build_animatedview_list_box.dart';

import '../../../../Features/App/common/ProductsDetails/presentation/pages/product_details.dart';
import '../../../Models/review_products_model.dart';
import 'title_box.dart';
import '../../../../Config/app_config.dart';

import '../../Enums/numbers_of_cross_axis_count.dart';
import 'card_product.dart';
import '../../../Resources/app_constants.dart';

class BuildCustomGridBuilder extends StatefulWidget {
  const BuildCustomGridBuilder({
    super.key,
    this.isSliver = true,
    this.builder,
    this.numbersOfCrossAxisCount = NumbersOfCrossAxisCount.small,
    this.title,
    this.model,
    this.loading,
    this.showAllItems = true,
  });

  final String? title;
  final bool? showAllItems;
  final bool isSliver;
  final Widget? builder;
  final bool? loading;
  final List<ReviewProductsModel>? model;
  final NumbersOfCrossAxisCount numbersOfCrossAxisCount;

  @override
  State<BuildCustomGridBuilder> createState() => _BuildCustomGridBuilderState();
}

class _BuildCustomGridBuilderState extends State<BuildCustomGridBuilder> {
  List<ReviewProductsModel> get _data {
    if (widget.model != null && widget.model!.isNotEmpty ||
        widget.loading == false) {
      return widget.model!;
    } else {
      return List.generate(4, (index) => _defaultProduct(index));
    }
  }

  ReviewProductsModel _defaultProduct(int index) {
    return ReviewProductsModel(
      id: index,
      name: "منتج افتراضي ${index + 1}",
      price: "00.00",
      discountPrice: "00.00",
      primaryImage: PrimaryImage(
          imageUrl:
              "https://www.electricianlicense.com/wp-content/uploads/2020/06/placeholder-img.jpg"),
      description: '',
      additionalInfo: '',
      dimensions: '',
    );
  }

  @override
  Widget build(BuildContext context) {
    final itemCount = widget.loading == true
        ? 2
        : widget.showAllItems == true
            ? _data.length
            : (_data.length >= 8 ? 8 : _data.length);

    return widget.isSliver
        ? SliverMainAxisGroup(slivers: [
            if (widget.title != null)
              BuildCustomTitleBox(
                title: widget.title ?? "",
                isSliver: widget.isSliver,
                list: widget.model ?? [],
              ),
            SliverGrid.builder(
              gridDelegate: AppConstants.gridDelegateSmallBuilder(
                widget.numbersOfCrossAxisCount,
              ),
              itemCount: itemCount,
              itemBuilder: _itemBuilder(),
            )
          ])
        : GridView.builder(
            shrinkWrap: true,
            physics: AppConfig.physicsCustomScrollView,
            gridDelegate: AppConstants.gridDelegateSmallBuilder(
              widget.numbersOfCrossAxisCount,
            ),
            itemCount: itemCount,
            itemBuilder: _itemBuilder(),
          );
  }

  NullableIndexedWidgetBuilder _itemBuilder() => (context, index) {
        return BuildAnimatedviewListBox(
          index: index,
          child: Skeletonizer(
            enabled: widget.loading ?? true,
            child: OpenContainer(
              transitionDuration: Duration(milliseconds: 500),
              openBuilder: (context, action) {
                return ProductDetailsScreen(id: _data[index].id);
              },
              closedBuilder: (_, openContainer) =>
                  widget.builder ??
                  BuildCardProduct(
                    item: _data[index],
                  ),
            ),
          ),
        );
      };
}
