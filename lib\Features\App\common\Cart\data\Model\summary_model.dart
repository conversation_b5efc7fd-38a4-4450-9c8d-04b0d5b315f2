
class SummaryModel {
  SummaryModel({
    required this.subtotal,
    required this.couponDiscount,
    required this.deliveryCharge,
    required this.grandTotal,
  });

  final int subtotal;
  final dynamic couponDiscount;
  final int deliveryCharge;
  final int grandTotal;

  factory SummaryModel.fromJson(Map<String, dynamic> json) {
    return SummaryModel(
      subtotal: json["subtotal"] ?? 0,
      couponDiscount: json["coupon_discount"] ?? 0,
      deliveryCharge: json["delivery_charge"] ?? 0,
      grandTotal: json["grand_total"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    "subtotal": subtotal,
    "coupon_discount": couponDiscount,
    "delivery_charge": deliveryCharge,
    "grand_total": grandTotal,
  };
}