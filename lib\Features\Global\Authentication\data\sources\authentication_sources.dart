import '../../../../../Core/Storage/Remote/api_response_model.dart';
import '../../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../../Core/Storage/Local/local_storage_service.dart';
import '../Models/login_model.dart';
import '../../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../../Core/Storage/Remote/api_service.dart';
import '../Models/register_model.dart';
import '../Models/user_data_model.dart';

class AuthenticationSources {
  static Future<ApiResponseModel<UserDataModel>> getUserDataByEmail(
      LoginModel loginModel) async {
    final response = await DioHelper.postData(
      data: loginModel.toMap(),
      path: ApiEndpoints.loginUser,
    );

    // Success case - extract data and token
    if (response.statusCode == 200) {
      // Save token if available
      if (response.data['access_token'] != null) {
        await LocalStorageService.setValue(
          LocalStorageKeys.token,
          response.data['access_token'],
        );
      }

      // Return user data
      if (response.data["user"] != null) {
        return ApiResponseModel<UserDataModel>(
          status: true,
          message: response.data["message"] ?? "Login successful",
          data: UserDataModel.fromJson(response.data["user"]),
        );
      }
    }

    // Extract API message from response for error case
    String message = _extractApiMessage(response.data);

    return ApiResponseModel<UserDataModel>(
      status: false,
      message: message.isNotEmpty ? message : "Login failed",
      data: null,
    );
  }

  static Future<ApiResponseModel<bool>> createAccount(
      RegisterModel registerModel) async {
    final response = await DioHelper.postData(
      data: registerModel.toMap(),
      path: ApiEndpoints.registerUser,
    );

    // Success case
    if ((response.statusCode == 200 || response.statusCode == 201)) {
      // Extract success message from API if available
      String message = _extractApiMessage(response.data);

      return ApiResponseModel<bool>(
        status: true,
        message: message.isNotEmpty ? message : "Account created successfully",
        data: true,
      );
    }

    // Extract error message from API
    String errorMessage = _extractApiMessage(response.data);

    return ApiResponseModel<bool>(
      status: false,
      message: errorMessage.isNotEmpty ? errorMessage : "Failed to create account",
      data: false,
    );
  }

  // Helper method to extract messages from API responses
  static String _extractApiMessage(dynamic data) {
    if (data == null) return '';

    if (data is Map<String, dynamic>) {
      // Try to get message from different common fields
      if (data.containsKey('message') && data['message'] is String) {
        return data['message'];
      }

      if (data.containsKey('error') && data['error'] is String) {
        return data['error'];
      }

      // Handle nested error objects
      if (data.containsKey('errors')) {
        var errors = data['errors'];
        if (errors is Map && errors.isNotEmpty) {
          var firstError = errors.values.first;
          if (firstError is List && firstError.isNotEmpty) {
            return firstError.first.toString();
          }
          return firstError.toString();
        } else if (errors is List && errors.isNotEmpty) {
          return errors.first.toString();
        }
      }
    } else if (data is String) {
      return data;
    }

    return '';
  }
}