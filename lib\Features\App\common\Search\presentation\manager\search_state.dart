
import '../../../../../../Core/Models/review_products_model.dart';

class SearchModelState {
 final bool? loading;
 final String? message;
 final bool? success;
 final List<ReviewProductsModel>? allProducts;
 final List<ReviewProductsModel>? searching;

  const SearchModelState({
    this.loading,
    this.message,
    this.success,
    this.allProducts=const [],
    this.searching,
  });

  SearchModelState copyWith({
    bool? loading,
    String? message,
    bool? success,
    List<ReviewProductsModel>? allProducts,
    List<ReviewProductsModel>? searching,
  }) {
    return SearchModelState(
      loading: loading ?? this.loading,
      message: message ?? this.message,
      success: success ?? this.success,
      allProducts: allProducts ?? this.allProducts,
      searching: searching ?? this.searching,
    );
  }
}
