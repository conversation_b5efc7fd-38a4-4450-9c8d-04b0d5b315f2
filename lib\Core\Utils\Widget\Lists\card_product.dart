import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../Features/App/common/Home/presentation/manager/home_state_model.dart';
import '../../../Models/review_products_model.dart';
import '../../../Resources/app_icons.dart';
import '../../../../Features/App/common/Home/presentation/manager/home_cubit.dart';
import '../Images/build_image.dart';
import '../../../Resources/app_fonts.dart';
import '../../../Resources/app_colors.dart';

class BuildCardProduct extends StatelessWidget {
  final ReviewProductsModel item;

  const BuildCardProduct({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 153.w,
      height: 222.h,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
              child: Stack(
            children: [
              Container(
                clipBehavior: Clip.antiAliasWithSaveLayer,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(8)),
                child: BuildImageAssets(
                  url: item.primaryImage?.imageUrl,
                  width: 153.w,
                  height: 167.h,
                ),
              ),
              calculateDiscountRatio(item.price, item.discountPrice)==""?SizedBox(): Positioned(
                top: 20.h,
                left: 5.w,
                child: RotationTransition(
                  turns: const AlwaysStoppedAnimation(-10 / 360),
                  child: Container(
                    height: 22.h,
                    width: 62.w,
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadiusDirectional.circular(50.r),
                    ),
                    child: Center(
                      child: Text(
                        "عرض ${calculateDiscountRatio(item.price, item.discountPrice)}",
                        style: AppTextStyles.bodyXtraSmallMedium
                            .copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 20.h,
                right: 5.w,
                child: BlocBuilder<HomeCubit, HomeStateModel>(
                  builder: (context, state) {
                    return InkWell(
                      onTap: () =>
                          context.read<HomeCubit>().toggleFavorite(item),
                      child:
                          context.read<HomeCubit>().isProductIn(item) == true
                              ? AppIcons.heartFill
                              : AppIcons.heartsOutline,
                    );
                  },
                ),
              )
            ],
          )),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  item.name,
                  style: AppTextStyles.bodyMediumSemiBold,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                ),
                10.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.discountPrice.isEmpty?item.price.split(".").first:item.discountPrice.split(".").first,
                      style: AppTextStyles.bodySmallSemiBold,
                    ),
                    6.horizontalSpace,
                   item.discountPrice.isEmpty?SizedBox(): Text(
                      item.price.split(".").first,
                      style: AppTextStyles.bodyXtraSmallMedium.copyWith(
                        color: AppColors.grayscale60,
                        decoration: TextDecoration.lineThrough,
                        decorationThickness: 3.w,
                        decorationColor: AppColors.grayscale60,
                        decorationStyle: TextDecorationStyle.solid,
                      ),
      
                      // style: context.getTextStyle.headlineMedium,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

String calculateDiscountRatio(String price, String discountPrice) {
  if (price.isEmpty || discountPrice.isEmpty) return "";

  num originalPrice = num.tryParse(price) ?? 0;
  num discountedPrice = num.tryParse(discountPrice) ?? 0;

  if (originalPrice == 0 || discountedPrice == 0) return "";

  num discountPercentage =
      ((originalPrice - discountedPrice) / originalPrice) * 100;
  return "${discountPercentage.toStringAsFixed(0)}%";
}
