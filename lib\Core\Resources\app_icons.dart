import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../Config/Assets/image_png.dart';
import '../../Config/Assets/image_svg.dart';
import 'app_colors.dart';

class AppIcons {
  //* public && auth

  static const FaIcon passwordVisibility =
      FaIcon(FontAwesomeIcons.eye, color: AppColors.primaryColor);
  static const FaIcon passwordVisibilityOff =
      FaIcon(FontAwesomeIcons.eyeSlash, color: AppColors.primaryColor);
  static FaIcon heartsOutline =
      FaIcon(FontAwesomeIcons.heart, color: AppColors.grayscale70);
  static FaIcon heartFill =
      FaIcon(FontAwesomeIcons.solidHeart, color: AppColors.primaryColor);
  static FaIcon check =
      FaIcon(FontAwesomeIcons.circleCheck, color: AppColors.success);
  static FaIcon uncheck =
      FaIcon(FontAwesomeIcons.xmark, color: AppColors.error);
  static FaIcon trash =
      FaIcon(FontAwesomeIcons.trashCan, color: AppColors.error);

  //* Bottom Navigation Bar
  static FaIcon compassOutline =
      FaIcon(FontAwesomeIcons.compass, color: AppColors.grayscale70);
  static FaIcon compassFill =
      FaIcon(FontAwesomeIcons.solidCompass, color: AppColors.primaryColor);
  static FaIcon profileOutline =
      FaIcon(FontAwesomeIcons.user, color: AppColors.grayscale70);
  static FaIcon profileFill =
      FaIcon(FontAwesomeIcons.solidUser, color: AppColors.primaryColor);
  static FaIcon searchOutline =
      FaIcon(FontAwesomeIcons.magnifyingGlass, color: AppColors.grayscale70);
  static FaIcon searchFill =
      FaIcon(FontAwesomeIcons.magnifyingGlass, color: AppColors.primaryColor);

  static SvgPicture homeOutline = SvgPicture.asset(
    AppImagesSvg.homeOutline,
  );
  static SvgPicture homeFill = SvgPicture.asset(
    AppImagesSvg.homeFill,
  );
  static SvgPicture scan = SvgPicture.asset(AppImagesSvg.scanImage);

  //* Floating Services
  static Widget carpenter = SvgPicture.asset(AppImagesSvg.carpenter);
  static Widget trucks = SvgPicture.asset(AppImagesSvg.truck);
  static Widget rentTools = Image.asset(AppImagesPng.toolbox);

  static FaIcon iconsServicesIn =
      FaIcon(FontAwesomeIcons.bars, color: AppColors.grayscale70);
  static FaIcon iconsServicesOut =
      FaIcon(FontAwesomeIcons.barsStaggered, color: AppColors.primaryColor);
  static FaIcon chat =
      FaIcon(FontAwesomeIcons.rocketchat, color: AppColors.primaryColor);

  //=====================================================================================
  //* with && without (backGround)
  static SvgPicture bagWithBackGround =
      SvgPicture.asset(AppImagesSvg.bagWithBackGround, fit: BoxFit.contain);
  static SvgPicture bag =
      SvgPicture.asset(AppImagesSvg.bag, fit: BoxFit.contain);
  static SvgPicture notification = SvgPicture.asset(
    AppImagesSvg.notificationWithBackGround,
    fit: BoxFit.contain,
  );
  static Widget notificationWithoutBackGround =
      SvgPicture.asset(AppImagesSvg.notificationWithoutBackGround);

//=====================================================================================
  //* Profile
  static Widget edit = SvgPicture.asset(AppImagesSvg.edit);
  static Widget card = SvgPicture.asset(AppImagesSvg.card);
  static Widget shield = SvgPicture.asset(AppImagesSvg.shield);
  static Widget globe = SvgPicture.asset(AppImagesSvg.global);
  static Widget circleInfo = SvgPicture.asset(AppImagesSvg.info);

  //=====================================================================================
  //* WorkshopDashboard
  static Widget workshopDashboardDefualt = Image.asset(
    AppImagesPng.workshopDashboard,
    height: 25,
    width: 25,
  );

  static Widget workshopDashboardDefualtHome = Container(
      padding: EdgeInsets.all(7),
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: Color(0x8C000000),
        shape: BoxShape.circle,
      ),
      child: Image.asset(
        AppImagesPng.workshopDashboardWhite,
      ));

  static Widget workshopDashboardSettingProfile = Image.asset(
    AppImagesPng.workshopDashboardBlack,
    height: 30,
    width: 30,
  );  
  
  static Widget workshopDashboardMainScreen = Image.asset(
    AppImagesPng.workshopDashboardGray,
  );
  static FaIcon logout =
      FaIcon(FontAwesomeIcons.arrowRightFromBracket, color: AppColors.error);

  //* WorkshopDashboard
  static Widget orderCarpenterActive =
      SvgPicture.asset(AppImagesSvg.orderCarpenterActive);
  static Widget orderCarpenterDisActive =
      SvgPicture.asset(AppImagesSvg.orderCarpenterDisActive);

  static FaIcon toogleoff =
      FaIcon(FontAwesomeIcons.toggleOff, color: AppColors.secondaryColor);

  static FaIcon toogleon =
      FaIcon(FontAwesomeIcons.toggleOn, color: AppColors.primaryColor);
}
