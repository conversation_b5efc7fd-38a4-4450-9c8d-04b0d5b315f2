
class CreateOrdersModel {
  final int serviceId;
  final int addressId;
  final DateTime appointmentDate;
  final String appointmentTimeSlot;
  final int carpentersRequired;
  final List<int> carpenterIds;
  final String paymentMethod;
  final String? notes;

  CreateOrdersModel({
    required this.serviceId,
    required this.addressId,
    required this.appointmentDate,
    required this.appointmentTimeSlot,
    required this.carpentersRequired,
    required this.carpenterIds,
    required this.paymentMethod,
    this.notes,
  });


  factory CreateOrdersModel.fromMap(Map<String,dynamic> map) {

    return CreateOrdersModel(
      serviceId: map['service_id'],
      addressId: map['address_id'],
      appointmentDate: DateTime.parse(map['appointment_date']),
      appointmentTimeSlot: map['appointment_time_slot'],
      carpentersRequired: map['carpenters_required'],
      carpenterIds: List<int>.from(map['carpenter_ids']),
      paymentMethod: map['payment_method'],
      notes: map['notes'],
    );
  }
  Map<String, dynamic> toMap() {
    return {
      'service_id': serviceId,
      'address_id': addressId,
      'appointment_date': appointmentDate.toString(),
      'appointment_time_slot': appointmentTimeSlot,
      'carpenters_required': carpentersRequired,
      'carpenter_ids': carpenterIds.map((map) => map).toList() ,
      'payment_method': paymentMethod,
      'notes': notes,
    };
  }
}