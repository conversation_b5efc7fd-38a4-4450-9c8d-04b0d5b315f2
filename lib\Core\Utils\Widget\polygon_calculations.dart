import 'package:vector_math/vector_math_64.dart' as math;

/// Calculates the area of a polygon defined by 3D points
/// The calculation is done by projecting the points onto the XZ plane
/// and using the Shoelace formula (<PERSON><PERSON><PERSON>'s area formula)
double calculatePolygonArea(List<math.Vector3> points) {
  if (points.length < 3) return 0.0;

  double sum = 0.0;
  for (int i = 0; i < points.length; i++) {
    final p1 = points[i];
    final p2 = points[(i + 1) % points.length];
    sum += (p1.x * p2.z) - (p2.x * p1.z);
  }

  return sum.abs() / 2;
}

/// Calculates the distance between two 3D points
double calculateDistance(math.Vector3 p1, math.Vector3 p2) {
  return (p2 - p1).length;
}

/// Converts square meters to square feet
double squareMetersToSquareFeet(double squareMeters) {
  return squareMeters * 10.764;
}

/// Converts square meters to square centimeters
double squareMetersToSquareCentimeters(double squareMeters) {
  return squareMeters * 10000;
}

/// Determines if the measuring area is small (less than 1 square meter)
bool isSmallArea(double areaInSquareMeters) {
  return areaInSquareMeters < 1.0;
}

/// Formats the area measurement with the appropriate unit
String formatAreaMeasurement(double area, bool isSmallArea) {
  if (isSmallArea) {
    return "${area.toStringAsFixed(2)} سم²";
  } else {
    return "${area.toStringAsFixed(2)} قدم²";
  }
}