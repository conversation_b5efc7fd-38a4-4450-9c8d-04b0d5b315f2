import 'package:dartz/dartz.dart';
import '../entities/product_details_model.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../entities/add_to_cart.dart';

abstract class ProductDetailsRepo {
  Future<Either<Failure, ViewProductDetails>> getProductDetails(
      {required int id});

  Future<Either<Failure, bool>> addToCart({required AddToCart item});
  Future<Either<Failure, Review>> addReview({required int  rating , String? comment,required int productId});
}
