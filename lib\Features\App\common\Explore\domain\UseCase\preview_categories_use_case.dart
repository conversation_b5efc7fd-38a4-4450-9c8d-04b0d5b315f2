import 'package:dartz/dartz.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repositories/explore_repo_impl.dart';


class PreviewCategoriesUseCase {
  final ExploreRepoImpl exploreRepoImpl;

  PreviewCategoriesUseCase({required this.exploreRepoImpl});

  Future<Either<Failure, List<ReviewProductsModel>>> call(
          {required int id}) async =>
      await exploreRepoImpl.getCategoriesDetails(id: id);
}
