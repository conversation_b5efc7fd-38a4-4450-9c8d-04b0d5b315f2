import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../Global/Authentication/presentation/components/build_social_login_button.dart';

class BuildButtonBottomScreen extends StatelessWidget {
  final VoidCallback onPressed;
  final String? price;
  final String? disCountPrice;

  final String nameButtonBuy;

  const BuildButtonBottomScreen({
    super.key,
    required this.onPressed,
    required this.price,
    required this.nameButtonBuy,
    this.disCountPrice = "",
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.backgroundColor.withAlpha(220),
      padding:
          EdgeInsets.symmetric(horizontal: AppConfig.customPaddingFromRightLeft)
              .r,
      margin: EdgeInsets.only(bottom: 5).r,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "EGP ",
                  style: AppTextStyles.h5Bold.copyWith(
                    color: AppColors.primaryColor,
                  ),
                ),
                disCountPrice?.isNotEmpty == true
                    ? TextSpan(
                        text: disCountPrice.toString().split(".00").first,
                        style: AppTextStyles.h3Bold.copyWith(
                          color: AppColors.textColorBlack,
                        ),
                      )
                    : TextSpan(
                        text: price.toString().split(".00").first,
                        style: AppTextStyles.h3Bold.copyWith(
                          color: AppColors.textColorBlack,
                        ),
                      ),
              ],
            ),
          ),
          CustomBuildButtonApp(
            size: Size(200.w, 56.h),
            text: nameButtonBuy,
            textStyle: AppTextStyles.h5Bold.copyWith(
              color: AppColors.textColorWhite,
            ),
            isSpace: false,
            backgroundColor: AppColors.primaryColor,
            onPressed: onPressed,
          )
        ],
      ),
    );
  }
}
