import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../Config/Cubit/settings_cubit.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Utils/Extensions/widget_extension.dart';
import '../../../../../../Core/Utils/Widget/AppBar/default_app_bar.dart';
import '../../data/Model/message.dart';
import '../../domain/UseCase/get_faqs_use_case.dart';
import '../../domain/repository/chat_ai_repo_imp.dart';
import '../Cubit/chat_ai_cubit.dart';
import '../../../../User/presentation/Cubit/user_cubit.dart';

import '../../data/Model/send_message.dart';
import '../../domain/UseCase/get_messages_use_case.dart';
import '../../domain/UseCase/send_messages_use_case.dart';
import '../components/frequently_asked_questions.dart';

class ChatAiScreen extends StatelessWidget {
  const ChatAiScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ChatAiCubit(
        GetFaqsUseCase(ChatAiRepoImp()),
        GetMessagesUseCase(ChatAiRepoImp()),
        SendMessagesUseCase(ChatAiRepoImp()),
      )..init(),
      child: BlocBuilder<ChatAiCubit, ChatAiStateModel>(
        builder: (context, state) {
          final chatAiCubit = context.read<ChatAiCubit>();
          final locale =
              context.watch<SettingsCubit>().state.locale.languageCode;
          final rtl = context.isRtl;

          return [
            SliverFillRemaining(
              fillOverscroll: true,
              child: Column(
                children: [
                  FrequentlyAskedQuestions(chatAiCubit: chatAiCubit, locale: locale,state: state,),
                  Expanded(
                    child: Chat(
                      messages: [
                        ...(state.messages
                            ?.map((e) => toTextMessage(e, context))
                            .toList() ??
                            []),
                      ],
                      onSendPressed: (partialText) {
                        final message = toSendMessage(partialText, locale);
                        chatAiCubit.sendMessage(message);
                      },
                      user: types.User(
                        id: context
                            .watch<UserCubit>()
                            .state
                            .userDataModel
                            .role
                            .toString(),
                        firstName:
                        context.watch<UserCubit>().state.userDataModel.name,
                        imageUrl: context
                            .watch<UserCubit>()
                            .state
                            .userDataModel
                            .photoUrl,
                      ),
                      l10n: rtl ? ChatL10nAr() : ChatL10nEn(),
                      scrollPhysics: AppConfig.physicsCustomScrollView,
                      inputOptions: InputOptions(
                        keyboardType: TextInputType.text,

                      ),
                      showUserNames: true,
                      showUserAvatars: true,
                      theme: DefaultChatTheme(
                        inputBackgroundColor: AppColors.primaryColor,
                        userNameTextStyle: AppTextStyles.bodyLargeBold,
                        primaryColor: AppColors.primaryColor,
                        backgroundColor: AppColors.backgroundColor,
                        receivedMessageBodyTextStyle: AppTextStyles.bodyLargeMedium.copyWith(color:AppColors.textColorBlack),
                        sentMessageBodyTextStyle: AppTextStyles.bodyLargeMedium.copyWith(color:AppColors.textColorWhite),
                        userAvatarNameColors: [AppColors.primaryColor, AppColors.secondaryColor],
                        inputTextStyle: AppTextStyles.bodyLargeMedium.copyWith(color:AppColors.textColorWhite),
                      )

                    ),
                  ),],
              ),
            )
          ].styledAppPages(
            withAll: true,
            widgetAppbar: DefaultAppBar(local: context.local.SmartAssistantFeature),
            appbar: true,
          );
        },
      ),
    );
  }

  types.TextMessage toTextMessage(MessageModel message, BuildContext context) {
    return types.TextMessage(
      author: types.User(
        id: message.role,
        firstName: "Zan",
        imageUrl:
            "https://github.com/felopatersameh/Models3DCreator/blob/main/ic_launcher_foreground.png?raw=true",
      ),
      createdAt: message.createdAt?.millisecondsSinceEpoch,
      id: message.id.toString(),
      text: message.content,
    );
  }

  SendMessage toSendMessage(types.PartialText message, String locale) {
    return SendMessage(message: message.text, lang: locale);
  }
}

