import 'package:dartz/dartz.dart';
import '../../data/Models/create_orders.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repository/carpenter_services_repo_impl.dart';

class CreateOrdersUseCase {
  final CarpenterServicesRepoImpl repoImpl;

  CreateOrdersUseCase(this.repoImpl);

  Future<Either<Failure, bool>> call(CreateOrdersModel order) async =>
      await repoImpl.createOrders(order);
}
