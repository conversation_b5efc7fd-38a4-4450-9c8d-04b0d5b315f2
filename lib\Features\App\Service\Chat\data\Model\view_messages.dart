import 'message.dart';

class ViewMessages {
  ViewMessages({
    required this.messages,
    required this.isAuthenticated,
  });

  final List<MessageModel> messages;
  final bool isAuthenticated;

  factory ViewMessages.fromJson(Map<String, dynamic> json){
    return ViewMessages(
      messages: json["messages"] == null ? [] : List<MessageModel>.from(json["messages"]!.map((x) => MessageModel.fromJson(x))),
      isAuthenticated: json["is_authenticated"] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    "messages": messages.map((x) => x.toJson()).toList(),
    "is_authenticated": isAuthenticated,
  };

}

