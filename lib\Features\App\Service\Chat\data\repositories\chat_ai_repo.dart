import 'package:dartz/dartz.dart';
import '../Model/faqs_model.dart';
import '../Model/send_message.dart';
import '../Model/view_messages.dart';

import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../Model/message.dart';

abstract class ChatAIRepo {
  Future<Either<Failure, List<MessageModel>>> sendMessage(
      {required SendMessage sendMessage});

  Future<Either<Failure, ViewMessages>> getMessages();

  Future<Either<Failure, List<FaqsModel>>> getFaqs();
}
