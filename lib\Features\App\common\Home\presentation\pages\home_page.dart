import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Utils/Extensions/widget_extension.dart';
import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Resources/app_constants.dart';
import '../../../../../../Core/Utils/Widget/Lists/custom_sliver_grid_builder.dart';
import '../manager/home_cubit.dart';
import '../manager/home_state_model.dart';
import '../widgets/build_sliver_app_bar_home.dart';
import '../widgets/offers_builder.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeStateModel>(
      builder: (context, state) {
        return [
          SliverAppBarHome(),
          CustomOffersWidget(),
          SliverPadding(
            padding: EdgeInsets.symmetric(
              horizontal: AppConfig.customPaddingFromRightLeft,
            ).r,
            sliver: BuildCustomGridBuilder(
              showAllItems: false,
              title: context.local.popular,
              model: state.productsPopular,
              loading: state.isLoading[AppConstants.kLoadingProductsPopular],
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.symmetric(
              horizontal: AppConfig.customPaddingFromRightLeft,
            ).r,
            sliver: BuildCustomGridBuilder(
              showAllItems: false,
              title: context.local.LatestProduct,
              model: state.productsFeatured,
              loading: state.isLoading[AppConstants.kLoadingProductsFeatured],
            ),
          ),
        ].styledAppPages(
          withScroll: true,
        );
      },
    );
  }
}
