import 'package:dartz/dartz.dart';

import '../../../ProductsDetails/data/entities/product_details_model.dart';
import '../../../../../../Core/Storage/Remote/api_error_handler.dart';
import '../repositories/home_repo_impl.dart';

class ProductDetailsUseCase {
  final HomeRepoImpl homeRepoImpl;

  ProductDetailsUseCase(this.homeRepoImpl);

  Future<Either<Failure, ViewProductDetails>> call({required int id}) async =>
      await homeRepoImpl.getProductDetails(id);
}
