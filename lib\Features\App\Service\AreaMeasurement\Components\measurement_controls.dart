import 'package:flutter/material.dart';

class MeasurementControls extends StatelessWidget {
  final VoidCallback onUndo;
  final VoidCallback onClear;

  const MeasurementControls({
    super.key,
    required this.onUndo,
    required this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        FloatingActionButton(
          onPressed: onUndo,
          backgroundColor: Colors.red,
          tooltip: "تراجع عن آخر نقطة",
          child: const Icon(Icons.undo, color: Colors.white),
        ),
        FloatingActionButton(
          onPressed: onClear,
          backgroundColor: Colors.orange,
          tooltip: "مسح جميع النقاط",
          child: const Icon(Icons.clear, color: Colors.white),
        ),
      ],
    );
  }
}