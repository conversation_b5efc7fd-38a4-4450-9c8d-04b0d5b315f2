import '../../../ProductsDetails/data/entities/product_details_model.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Storage/Remote/api_endpoints.dart';
import '../../../../../../Core/Storage/Remote/api_service.dart';


class HomeData {
  static Future<List<ReviewProductsModel>> getDataProductIsFeatured() async {
    final response = await DioHelper.getData(
      path: ApiEndpoints.productFeatured,
    );

    if (response.data["data"] == null) {
      return [];
    }

    return (response.data["data"] as List)
        .map(
          (item) => ReviewProductsModel.fromJson(
        (item),
      ),
    )
        .toList();
  }
  static Future<List<ReviewProductsModel>> getDataProductIsPopular() async {
    final response = await DioHelper.getData(
      path: ApiEndpoints.productPopular,
    );
    if (response.data["data"] == null) {
      return [];
    }

    return (response.data["data"] as List)
        .map(
          (item) => ReviewProductsModel.fromJson(
        item,
      ),
    )
        .toList();
  }
  static Future<ViewProductDetails> getProductsByID(int id) async {
    final response = await DioHelper.postData(
      data: id,
      path: ApiEndpoints.productDetails,
    );

    return (response.data["data"]).map(
          (item) => ViewProductDetails.fromJson(
        item,
      ),
    );
  }


}

