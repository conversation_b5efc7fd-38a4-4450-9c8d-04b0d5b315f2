import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../domain/entities/payment_input_entities.dart';
import '../../../domain/use_cases/payment_use_case.dart';

part 'payment_state.dart';

class PaymentCubit extends Cubit<PaymentState> {
  PaymentCubit(this.paymentUseCase) : super(StripPaymentInitial());
  final PaymentStripUseCase paymentUseCase;


  Future makeStripPaymentStrip(
      {required PaymentInputEntities paymentInputEntities}) async {
    emit(Loading());
    await paymentUseCase
        .execute(paymentInputEntities: paymentInputEntities)
        .then((onValue) => onValue.fold(
            (failure) {
              emit(Failure(error: failure.toString()));
            },
            (item) => emit(Success(item: item))));
  }

  @override
  void onChange(Change<PaymentState> change) {
    log(change.toString());
    super.onChange(change);
  }
}
