import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Utils/Widget/Images/view_image_user.dart';
import '../../../../User/presentation/Cubit/user_cubit.dart';
import '../../../../../../Config/app_config.dart';
import '../../../../../../Core/Utils/Extensions/context_extension.dart';

import '../../../../../../Config/Assets/image_png.dart';
import '../../../../../../Config/Routes/route_name.dart';
import '../../../../../../Core/Resources/app_colors.dart';
import '../../../../../../Core/Resources/app_fonts.dart';
import '../../../../../../Core/Resources/app_icons.dart';
import '../../../../../../main.dart';

class SliverAppBarHome extends StatelessWidget {
  const SliverAppBarHome({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 300.h,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          children: [
            Positioned.fill(
              child: ColorFiltered(
                  colorFilter: ColorFilter.mode(
                      Color(
                        0xff171725,
                      ),
                      BlendMode.screen),
                  child: Image.asset(
                    AppImagesPng.appBarHome,
                    fit: BoxFit.cover,
                  )),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 60.h,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(45),
                  ),
                ),
                child: Center(
                    child: Container(
                  width: 74.w,
                  height: 8.h,
                  decoration: BoxDecoration(
                    color: AppColors.grayscale30,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                )),
              ),
            )
          ],
        ),
      ),
      toolbarHeight: 80.h,
      backgroundColor: AppColors.backgroundColor,
      scrolledUnderElevation: 0,
      pinned: true,
      leadingWidth: 100.w,
      leading: context.watch<UserCubit>().id == -1
          ? null
          : Padding(
              padding: const EdgeInsets.all(10.0),
              child: ViewImageUser(),
            ),
      title: context.watch<UserCubit>().id == -1 ? null : _buildTitle(context),
      elevation: 0,
      actions: [
         context.isCarpenter ? IconButton(
            iconSize: 30,
            icon: AppIcons.workshopDashboardDefualtHome,
            onPressed: () async {
              context.read<UserCubit>().getUserData();
              kNavigationService.navigateTo(AppRoutes.dashBoredMainScreen);
              // LocationService location =LocationService();
              // await  location.getDetailsAddressByLatLong();
              // await  location.getDetailsAddressByAddress("HGX6+8C7, , Kafr Al Hosr, Al-Sharqia Governorate 7122073, Egypt");
            }):const SizedBox(),
        12.horizontalSpace,
        InkWell(
          onTap: () => kNavigationService.navigateTo(AppRoutes.cart),
          child: AppIcons.bagWithBackGround,
        ),
        AppConfig.customPaddingFromRightLeft.horizontalSpace,
      ],
    );
  }

  Column _buildTitle(BuildContext context, [bool black = false]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text(
          context.watch<UserCubit>().state.userDataModel.name,
          style: AppTextStyles.h6SemiBold.copyWith(
            color: black ? AppColors.grayscale90 : AppColors.primaryColor,
          ),
        ),
        Text(
          context.getGreeting(),
          style: AppTextStyles.bodyXtraSmallMedium.copyWith(
            color: black
                ? AppColors.grayscale90
                : AppColors.primaryColor.withAlpha(200),
          ),
        ),
      ],
    );
  }
}
