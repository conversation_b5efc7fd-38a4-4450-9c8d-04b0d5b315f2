// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "AddAddress": MessageLookupByLibrary.simpleMessage("أضف العنوان"),
        "AddReview": MessageLookupByLibrary.simpleMessage("أضف مراجعة"),
        "AddToCart": MessageLookupByLibrary.simpleMessage("أضف إلى السلة"),
        "Address": MessageLookupByLibrary.simpleMessage("العنوان"),
        "ApplyFilters": MessageLookupByLibrary.simpleMessage("تطبيق المرشحات"),
        "Back": MessageLookupByLibrary.simpleMessage("السابق"),
        "Bio": MessageLookupByLibrary.simpleMessage("السيرة الذاتية"),
        "BuyNow": MessageLookupByLibrary.simpleMessage("اشتري الآن"),
        "CM": MessageLookupByLibrary.simpleMessage("سم"),
        "Camera": MessageLookupByLibrary.simpleMessage("كاميرا"),
        "Carpenter": MessageLookupByLibrary.simpleMessage("نجارون محترفون"),
        "CarpenterDes": MessageLookupByLibrary.simpleMessage(
            "سيساعدك النجارون في اختيار الأنسب لك."),
        "Carpenter_service":
            MessageLookupByLibrary.simpleMessage("خدمة النجار"),
        "Carpentry_selection": MessageLookupByLibrary.simpleMessage(
            "حدد نوع الخدمة التي تحتاجها."),
        "Cash": MessageLookupByLibrary.simpleMessage("نقدي"),
        "Categories": MessageLookupByLibrary.simpleMessage("فئات"),
        "ChangePassword":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "Color": MessageLookupByLibrary.simpleMessage("لون"),
        "Confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
        "ConfirmationPassword":
            MessageLookupByLibrary.simpleMessage("كلمة مرور التأكيد"),
        "Continue": MessageLookupByLibrary.simpleMessage("واصل"),
        "CouponCode": MessageLookupByLibrary.simpleMessage("رمز القسيمة"),
        "CouponDiscount": MessageLookupByLibrary.simpleMessage("خصم القسيمة"),
        "CurrentPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور الحالية"),
        "CustomerInformation":
            MessageLookupByLibrary.simpleMessage("معلومات العميل"),
        "CustomerReviews":
            MessageLookupByLibrary.simpleMessage("مراجعات العملاء"),
        "Date": MessageLookupByLibrary.simpleMessage("التاريخ"),
        "DeliveryCharge": MessageLookupByLibrary.simpleMessage("رسوم التوصيل"),
        "Description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "Details": MessageLookupByLibrary.simpleMessage("التفاصيل"),
        "Discount": MessageLookupByLibrary.simpleMessage("خصم"),
        "Enter": MessageLookupByLibrary.simpleMessage(" ادخل"),
        "EstimatedCost":
            MessageLookupByLibrary.simpleMessage("التكلفة التقديرية"),
        "Explore": MessageLookupByLibrary.simpleMessage("استكشاف"),
        "Favourite": MessageLookupByLibrary.simpleMessage("المفضلة"),
        "Featured": MessageLookupByLibrary.simpleMessage("مميز"),
        "Filter": MessageLookupByLibrary.simpleMessage("فلتر"),
        "FurnitureMeasurementFeature":
            MessageLookupByLibrary.simpleMessage("قياس أبعاد الأثاث"),
        "FurnitureRecommendationFeature":
            MessageLookupByLibrary.simpleMessage("توصيات واقتراحات للأثاث"),
        "GoodMorning": MessageLookupByLibrary.simpleMessage("صباح الخير"),
        "GoodNight": MessageLookupByLibrary.simpleMessage("مساء خير"),
        "Home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "IDCard_Passport": MessageLookupByLibrary.simpleMessage("بطاقة الهوية"),
        "IDCard_PassportDescription": MessageLookupByLibrary.simpleMessage(
            "رفع صورة واضحة من بطاقة الهوية."),
        "InStock": MessageLookupByLibrary.simpleMessage("متوفر"),
        "Info": MessageLookupByLibrary.simpleMessage("المساعدة والدعم"),
        "Languages": MessageLookupByLibrary.simpleMessage("اللغات"),
        "LatestProduct": MessageLookupByLibrary.simpleMessage("أحدث منتجات"),
        "Length": MessageLookupByLibrary.simpleMessage("الطول"),
        "LocationInformation":
            MessageLookupByLibrary.simpleMessage("معلومات الموقع"),
        "Login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "LoginByGoogle":
            MessageLookupByLibrary.simpleMessage("تسجيل باستخدام جوجل"),
        "Logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "NewPassword":
            MessageLookupByLibrary.simpleMessage("كلمة المرور الجديدة"),
        "Next": MessageLookupByLibrary.simpleMessage("التالي"),
        "NoImageSelected":
            MessageLookupByLibrary.simpleMessage("لا يوجد صورة محددة"),
        "Note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
        "Notifications": MessageLookupByLibrary.simpleMessage("إشعارات"),
        "OnlinePayment":
            MessageLookupByLibrary.simpleMessage("دفع عبر الإنترنت"),
        "OrderDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الطلب"),
        "OrderNames": MessageLookupByLibrary.simpleMessage("اسم الطلب"),
        "OrderStatus": MessageLookupByLibrary.simpleMessage("حالة الطلب"),
        "OutStock": MessageLookupByLibrary.simpleMessage("غير متوفر"),
        "Photo": MessageLookupByLibrary.simpleMessage("صورة"),
        "PickTime": MessageLookupByLibrary.simpleMessage("اختر الوقت"),
        "PoliceRecord": MessageLookupByLibrary.simpleMessage("السجل الجنائي"),
        "PoliceRecordDescription": MessageLookupByLibrary.simpleMessage(
            "رفع صورة واضحة من السجل الجنائي."),
        "Price": MessageLookupByLibrary.simpleMessage("سعر"),
        "Proceed": MessageLookupByLibrary.simpleMessage("تابع"),
        "Profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "ProfilePhoto": MessageLookupByLibrary.simpleMessage("صورة الحساب"),
        "PromoCode": MessageLookupByLibrary.simpleMessage("كود الترويجي"),
        "Quantity": MessageLookupByLibrary.simpleMessage("كمية"),
        "Recommendation": MessageLookupByLibrary.simpleMessage("توصية مخصصة"),
        "RecommendationDes": MessageLookupByLibrary.simpleMessage(
            "التقط صورة لغرفتك وسأقدم لك أفضل التوصيات."),
        "Regular_cost": MessageLookupByLibrary.simpleMessage(
            "التكلفة العادية هي 252 جنيهًا. سيتم حساب التكلفة الإجمالية لاحقًا."),
        "Rent": MessageLookupByLibrary.simpleMessage("تأجير المعدات والمواد"),
        "RentDes": MessageLookupByLibrary.simpleMessage(
            "استأجر الأدوات والمواد التي تحتاجها لمشاريعك."),
        "Required_person":
            MessageLookupByLibrary.simpleMessage("الشخص المطلوب"),
        "ResetFilters":
            MessageLookupByLibrary.simpleMessage("إعادة تعيين مرشحات"),
        "Reviews": MessageLookupByLibrary.simpleMessage("المراجعات"),
        "RoomType": MessageLookupByLibrary.simpleMessage("نوع الغرفه"),
        "Save": MessageLookupByLibrary.simpleMessage("حفظ"),
        "Scan": MessageLookupByLibrary.simpleMessage("مسح ضوئي"),
        "Search": MessageLookupByLibrary.simpleMessage("بحث"),
        "Security": MessageLookupByLibrary.simpleMessage("حماية"),
        "SeeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "Service": MessageLookupByLibrary.simpleMessage("خدمات إضافية"),
        "ServiceCharge": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "ServiceDes": MessageLookupByLibrary.simpleMessage(
            "اكتشف المزيد من الخدمات المتاحة هنا."),
        "ServiceInformation":
            MessageLookupByLibrary.simpleMessage("معلومات الخدمة"),
        "ServiceName": MessageLookupByLibrary.simpleMessage("اسم الخدمة"),
        "Setting": MessageLookupByLibrary.simpleMessage("الاعدادات"),
        "SignUp": MessageLookupByLibrary.simpleMessage("انشاء حساب"),
        "Size": MessageLookupByLibrary.simpleMessage("حجم"),
        "Skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "SmartAssistantFeature":
            MessageLookupByLibrary.simpleMessage("المساعد الذكي"),
        "SmartAssistantFeatureDes": MessageLookupByLibrary.simpleMessage(
            "دردش معي وسأجيب على أي سؤال لديك حول الأثاث."),
        "StarRating": MessageLookupByLibrary.simpleMessage("تصنيف"),
        "Submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "Subtotal": MessageLookupByLibrary.simpleMessage("المجموع الفرعي"),
        "Time": MessageLookupByLibrary.simpleMessage("الوقت"),
        "TitleLogoSplash": MessageLookupByLibrary.simpleMessage(
            "اكتشف أفضل قطع الأثاث لمنزلك"),
        "TitleStartApp": MessageLookupByLibrary.simpleMessage(
            "ابحث عن أثاث أحلامك لمنزلك الأنيق"),
        "TotalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الكلي"),
        "Trucks": MessageLookupByLibrary.simpleMessage("خدمات النقل بالشاحنات"),
        "TrucksDes": MessageLookupByLibrary.simpleMessage(
            "نوفر لك شاحنات لنقل الأثاث الذي تحتاجه بسهولة."),
        "UploadImage": MessageLookupByLibrary.simpleMessage("رفع صورة"),
        "UploadImageCarpenter":
            MessageLookupByLibrary.simpleMessage("رفع صورة واضحة من نفسك."),
        "ViewOrders": MessageLookupByLibrary.simpleMessage("عرض"),
        "Width": MessageLookupByLibrary.simpleMessage("العرض"),
        "WorkingPrice": MessageLookupByLibrary.simpleMessage("ساعات العمل"),
        "Workshop": MessageLookupByLibrary.simpleMessage("الورشة"),
        "WorkshopDashboard":
            MessageLookupByLibrary.simpleMessage("لوحة التحكم"),
        "WorkshopDescription":
            MessageLookupByLibrary.simpleMessage("رفع صورة واضحة من الورشة."),
        "YourCard": MessageLookupByLibrary.simpleMessage("بطاقتك"),
        "YourRating": MessageLookupByLibrary.simpleMessage("تقييمك"),
        "accept": MessageLookupByLibrary.simpleMessage("قبول"),
        "accepted": MessageLookupByLibrary.simpleMessage("اتنهاء"),
        "address_line": MessageLookupByLibrary.simpleMessage("خط العنوان"),
        "age": MessageLookupByLibrary.simpleMessage("العمر"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "area": MessageLookupByLibrary.simpleMessage("منطقة"),
        "available": MessageLookupByLibrary.simpleMessage("متوفر"),
        "average_rating": MessageLookupByLibrary.simpleMessage("تقييم متوسط"),
        "back_confirmation":
            MessageLookupByLibrary.simpleMessage("مغادرة الصفحة"),
        "back_confirmation_message": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد مغادرة هذه الصفحة؟ سيتم فقدان أي تغييرات غير محفوظة."),
        "boarding1_body": MessageLookupByLibrary.simpleMessage(
            "تشكيلة واسعة ومتنوعة من الأثاث عالي الجودة وتصميمات رائعة."),
        "boarding1_title":
            MessageLookupByLibrary.simpleMessage("منتجات بجودة مضمونة"),
        "boarding2_body": MessageLookupByLibrary.simpleMessage(
            "نقدم ضمانًا شاملاً يضمن رضاك التام عن المنتج."),
        "boarding2_title":
            MessageLookupByLibrary.simpleMessage("ضمان شامل لراحة بالك"),
        "boarding3_body": MessageLookupByLibrary.simpleMessage(
            "سنساعدك في العثور على كل ما تحتاجه لمنزل أحلامك."),
        "boarding3_title": MessageLookupByLibrary.simpleMessage(
            "دعنا نحقق احتياجات منزلك معًا"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cart": MessageLookupByLibrary.simpleMessage("عربة التسوق"),
        "checkout": MessageLookupByLibrary.simpleMessage("تحقق من ذلك >"),
        "city": MessageLookupByLibrary.simpleMessage("مدينة"),
        "completed_orders":
            MessageLookupByLibrary.simpleMessage("طلبات مكتملة"),
        "completed_services":
            MessageLookupByLibrary.simpleMessage("خدمات مكتملة"),
        "confirmation_dialogs":
            MessageLookupByLibrary.simpleMessage("حوارات التأكيد"),
        "couponDiscount": MessageLookupByLibrary.simpleMessage("خصم القسيمة"),
        "delay": MessageLookupByLibrary.simpleMessage("تأخير"),
        "delete": MessageLookupByLibrary.simpleMessage("حذف"),
        "delete_all_item_confirmation":
            MessageLookupByLibrary.simpleMessage(" حذف جميع العناصر"),
        "delete_all_item_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                "هل أنت متأكد أنك تريد حذف جميع العناصر؟"),
        "delete_item_confirmation":
            MessageLookupByLibrary.simpleMessage("تأكيد حذف العنصر"),
        "delete_item_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                "هل أنت متأكد أنك تريد حذف هذا العنصر؟"),
        "dialog_examples":
            MessageLookupByLibrary.simpleMessage("أمثلة الحوارات"),
        "email": MessageLookupByLibrary.simpleMessage("بريد إلكتروني"),
        "error_dialog": MessageLookupByLibrary.simpleMessage("حوار الخطأ"),
        "error_message": MessageLookupByLibrary.simpleMessage(
            "حدث خطأ ما. يرجى المحاولة مرة أخرى."),
        "experience": MessageLookupByLibrary.simpleMessage("خبرة"),
        "forgetPassword":
            MessageLookupByLibrary.simpleMessage("هل نسيت كلمة المرور؟"),
        "hour": MessageLookupByLibrary.simpleMessage("ساعة واحدة"),
        "hourly_rate":
            MessageLookupByLibrary.simpleMessage("معدل السعر بالساعة"),
        "id_card_photo_url":
            MessageLookupByLibrary.simpleMessage("صورة بطاقة الهوية"),
        "info_saved": MessageLookupByLibrary.simpleMessage("تم حفظ المعلومات"),
        "item_deleted": MessageLookupByLibrary.simpleMessage("تم حذف العنصر"),
        "later": MessageLookupByLibrary.simpleMessage("لاحقًا"),
        "leave": MessageLookupByLibrary.simpleMessage("مغادرة"),
        "loading_dialog": MessageLookupByLibrary.simpleMessage("حوار التحميل"),
        "loading_dialog_title":
            MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "logout_confirmation":
            MessageLookupByLibrary.simpleMessage("تأكيد تسجيل الخروج"),
        "logout_confirmation_message": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد تسجيل الخروج؟"),
        "massageInCart":
            MessageLookupByLibrary.simpleMessage("آسف! لا توجد عناصر هنا بعد."),
        "messageChangeLanguageSuccess":
            MessageLookupByLibrary.simpleMessage("تم تغيير اللغة بنجاح"),
        "messageLoginSuccess":
            MessageLookupByLibrary.simpleMessage("تم تسجيل الدخول بنجاح"),
        "messageLogoutSuccess":
            MessageLookupByLibrary.simpleMessage("تم تسجيل الخروج بنجاح"),
        "messageRegisterSuccess":
            MessageLookupByLibrary.simpleMessage("تم التسجيل بنجاح"),
        "monthly_sales":
            MessageLookupByLibrary.simpleMessage("المبيعات الشهرية"),
        "name": MessageLookupByLibrary.simpleMessage(" الاسم"),
        "navigating_back": MessageLookupByLibrary.simpleMessage("جاري العودة"),
        "no": MessageLookupByLibrary.simpleMessage("لا"),
        "no_pending_orders":
            MessageLookupByLibrary.simpleMessage("لا يوجد طلبات معلقة"),
        "no_today_orders":
            MessageLookupByLibrary.simpleMessage("لا يوجد طلبات اليوم"),
        "offerPrice": MessageLookupByLibrary.simpleMessage("خصم"),
        "offerTittle": MessageLookupByLibrary.simpleMessage("أعلى منتجات جودة"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "operation_completed":
            MessageLookupByLibrary.simpleMessage("اكتملت العملية"),
        "orLogin": MessageLookupByLibrary.simpleMessage("هل لديك بالفعل حساب؟"),
        "orSingUp": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
        "order_submitted":
            MessageLookupByLibrary.simpleMessage("تم إرسال الطلب"),
        "orders": MessageLookupByLibrary.simpleMessage("الطلبات"),
        "password": MessageLookupByLibrary.simpleMessage(" كلمه المرور"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "pending_orders": MessageLookupByLibrary.simpleMessage("طلبات معلقة"),
        "phone": MessageLookupByLibrary.simpleMessage(" الموبيل"),
        "please_wait": MessageLookupByLibrary.simpleMessage("يرجى الانتظار..."),
        "police_record_photo_url":
            MessageLookupByLibrary.simpleMessage("صورة السجل الجنائي"),
        "popular": MessageLookupByLibrary.simpleMessage("شائع "),
        "postal_code": MessageLookupByLibrary.simpleMessage("رمز بريدي"),
        "profile_status": MessageLookupByLibrary.simpleMessage("حالة الحساب"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "reject": MessageLookupByLibrary.simpleMessage("رفض"),
        "running": MessageLookupByLibrary.simpleMessage("جاري"),
        "save_info_confirmation":
            MessageLookupByLibrary.simpleMessage("تأكيد حفظ المعلومات"),
        "save_info_confirmation_message":
            MessageLookupByLibrary.simpleMessage("هل تريد حفظ معلوماتك؟"),
        "services": MessageLookupByLibrary.simpleMessage("خدمات"),
        "state": MessageLookupByLibrary.simpleMessage("ولاية"),
        "status_dialogs": MessageLookupByLibrary.simpleMessage("حوارات الحالة"),
        "stay": MessageLookupByLibrary.simpleMessage("البقاء"),
        "submit_order_confirmation":
            MessageLookupByLibrary.simpleMessage("تأكيد إرسال الطلب"),
        "submit_order_confirmation_message":
            MessageLookupByLibrary.simpleMessage(
                "هل أنت متأكد أنك تريد إرسال هذا الطلب؟"),
        "success_dialog": MessageLookupByLibrary.simpleMessage("حوار النجاح"),
        "success_message":
            MessageLookupByLibrary.simpleMessage("تمت العملية بنجاح!"),
        "today_orders": MessageLookupByLibrary.simpleMessage("طلبات اليوم"),
        "total_earnings":
            MessageLookupByLibrary.simpleMessage("إجمالي الأرباح"),
        "warning_dialog": MessageLookupByLibrary.simpleMessage("حوار التحذير"),
        "warning_message":
            MessageLookupByLibrary.simpleMessage("قد يكون لهذا الإجراء عواقب."),
        "welcome": MessageLookupByLibrary.simpleMessage("مرحبا بك"),
        "workshop_photo_url":
            MessageLookupByLibrary.simpleMessage("صورة الورشة"),
        "years_experience":
            MessageLookupByLibrary.simpleMessage("سنوات الخبرة"),
        "yes": MessageLookupByLibrary.simpleMessage("نعم")
      };
}
