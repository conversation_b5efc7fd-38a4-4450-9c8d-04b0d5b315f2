import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../Core/Utils/Extensions/localizations_extension.dart';
import '../../../../../../Core/Utils/Widget/Massages/custom_massage.dart';
import '../../../../../../Core/Utils/Widget/Lists/custom_sliver_grid_builder.dart';
import '../../../../../../Core/Utils/Widget/Animations/loading_animation.dart';
import '../manager/search_cubit.dart';
import '../manager/search_state.dart';
import '../../../../../../../../Core/Utils/Extensions/widget_extension.dart';
import 'custom_search_text_field.dart';

class SearchViewBody extends StatefulWidget {
  const SearchViewBody({super.key});

  @override
  State<SearchViewBody> createState() => _SearchViewBodyState();
}

class _SearchViewBodyState extends State<SearchViewBody> {
  TextEditingController searchController = TextEditingController(text: "");

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SearchCubit, SearchModelState>(
      builder: (context, state) => [
        SliverAppBar(
          title: CustomSearchField(),
          pinned: true,
          toolbarHeight: 120.h,
          scrolledUnderElevation: 0,
        ),
        if (state.loading == true)
          SliverFillRemaining(
            hasScrollBody: false,
            fillOverscroll: true,
            child: CustomLoadingAnimation(),
          )
        else if (state.success == true && state.searching?.isNotEmpty == true)
          BuildCustomGridBuilder(
            model: state.searching,
            loading: state.searching == null,
          )
        else if (state.success == true && state.searching?.isEmpty == true)
            SliverFillRemaining(
              hasScrollBody: false,
              child: CustomMassage(massage: context.local.MessageEmptyProduct),
            )
          else
            BuildCustomGridBuilder(
              model: state.allProducts,
              loading: state.allProducts == null,
            ),
      ].styledAppPages(withAll: true),
    );
  }
}
