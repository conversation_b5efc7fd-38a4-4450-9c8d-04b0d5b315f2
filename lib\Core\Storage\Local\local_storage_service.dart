import 'package:hive/hive.dart';

import '../../Models/review_products_model.dart';
import 'local_storage_keys.dart';

class LocalStorageService {
  static late Box _box;

  static Future<void> init() async =>
      _box = await Hive.openBox(LocalStorageKeys.boxName);

  static Future<void> setValue(String key, dynamic value) async =>
      await _box.put(key, value);

  static dynamic getValue(String key, {dynamic defaultValue}) =>
      _box.get(key, defaultValue: defaultValue);

  static Future<void> removeValue(String key) async => await _box.delete(key);

  static Future<void> clear() async => await _box.clear();
}

class ProductStorageService {
  static late Box _box;

  static Future<void> init() async {
    _box = await Hive.openBox(LocalStorageKeys.boxName);
  }

  static Future<void> toggleProduct(ReviewProductsModel product) async {
    List<Map<dynamic, dynamic>> products = List<Map<dynamic, dynamic>>.from(
      _box.get(LocalStorageKeys.products, defaultValue: []),
    );

    final existingIndex =
        products.indexWhere((item) => item['id'] == product.id);

    if (existingIndex != -1) {
      products.removeAt(existingIndex);
    } else {
      products.add(product.toMap());
    }

    await _box.put(LocalStorageKeys.products, products);
  }

  static Future<void> removeProductById(int productId) async {
    List<Map<String, dynamic>> products = List<Map<String, dynamic>>.from(
      _box.get(LocalStorageKeys.products, defaultValue: []),
    );

    products.removeWhere((item) => item['id'] == productId);
    await _box.put(LocalStorageKeys.products, products);
  }

  static List<ReviewProductsModel> getAllProducts() {
    List<Map<String, dynamic>> products = List<Map<String, dynamic>>.from(
      _box.get(LocalStorageKeys.products, defaultValue: []),
    );

    return products
        .map((item) => ReviewProductsModel.fromJson(item))
        .toList();
  }

  static bool isProductInList(int productId) {
    List<Map<dynamic, dynamic>> products = List<Map<dynamic, dynamic>>.from(
      _box.get(LocalStorageKeys.products, defaultValue: []),
    );

    return products.any((item) => item['id'] == productId);
  }
}
