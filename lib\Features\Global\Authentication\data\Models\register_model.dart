class RegisterModel {
  final String name;
  final String email;
  final String password;
  final String passwordConfirmation;
  final String? phone;

  RegisterModel({required this.name, required this.email, required this.password, required this.passwordConfirmation, this.phone});

  factory RegisterModel.fromMap(dynamic map) {
    return RegisterModel(
      name: map['name'],
      email: map['email'],
      password: map['password'],
      passwordConfirmation: map['passwordConfirmation'],
      phone: map['phone'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'phone': phone,
    };
  }
}
