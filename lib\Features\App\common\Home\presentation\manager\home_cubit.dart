import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../../Config/Cubit/settings_cubit.dart';
import '../../../../../../Core/Models/review_products_model.dart';
import '../../../../../../Core/Resources/app_constants.dart';
import '../../../../../../Core/Storage/Local/local_storage_service.dart';

import '../../domain/useCase/get_featured.dart';
import '../../domain/useCase/get_popular.dart';
import '../../domain/useCase/product_details.dart';
import 'home_state_model.dart';

class HomeCubit extends Cubit<HomeStateModel> {
  HomeCubit(
    this.getFeaturedUseCase,
    this.getPopularUseCase,
    this.productDetailsUseCase,
  ) : super(
          HomeStateModel(),
        );
  final GetFeaturedUseCase getFeaturedUseCase;
  final GetPopularUseCase getPopularUseCase;
  final ProductDetailsUseCase productDetailsUseCase;

  Future<void> toggleFavorite(ReviewProductsModel model) async {
    await ProductStorageService.toggleProduct(model);
    await loadFavoriteProducts();
  }

  bool isProductIn(ReviewProductsModel model) {
    return ProductStorageService.isProductInList(model.id);
  }

  Future<void> loadFavoriteProducts() async {
    final favoriteProducts = ProductStorageService.getAllProducts();
    emit(state.copyWith(favoriteProductDetails: favoriteProducts));
  }

  Future<void> init(BuildContext context) async {
    context.read<SettingsCubit>().stream.listen((state) {
      if (state.internet) {
        _setLoading(AppConstants.kLoadingProductsFeatured, true);
        _setLoading(AppConstants.kLoadingProductsPopular, true);

        _getProductIsFeatured();
        _getProductIsPopular(); 
      }
    });
  }

  Future<void> _getProductIsFeatured() async {
    final response = await getFeaturedUseCase.call();
    response.fold(
      (failure) {},
      (data) {
        emit(state.copyWith(productsFeatured: data));
      },
    );

    _setLoading(AppConstants.kLoadingProductsFeatured, false);
  }

  Future<void> _getProductIsPopular() async {
    final response = await getPopularUseCase.call();
    response.fold(
      (failure) {},
      (data) {
        emit(state.copyWith(productsPopular: data));
      },
    );
    _setLoading(AppConstants.kLoadingProductsPopular, false);
  }

  Future<void> getProductByID(int id) async {
    final response = await productDetailsUseCase.call(id: id);
    response.fold(
      (failure) {},
      (data) {
        emit(state.copyWith(productDetails: data));
      },
    );
  }

  void _setLoading(String key, bool value) {
    emit(state.copyWith(
      isLoading: {
        ...state.isLoading,
        key: value,
      },
    ));
  }
}
